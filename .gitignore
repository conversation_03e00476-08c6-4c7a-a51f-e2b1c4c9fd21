# Node dependencies
node_modules/
npm-debug.log*
yarn-error.log

# Build output
www/
dist/
build/

# Ionic/Angular/React/Capacitor/Cordova generated folders
.platforms/
plugins/
plugins/android.json
plugins/ios.json
plugins/fetch.json
plugins/cordova-plugin-whitelist
platforms/
.cordova/
coverage/
.idea/
.vscode/
.sourcemaps/
*.sourcemap

# Environment files
.env
.env.*

# macOS system files
.DS_Store

# Xcode
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/
*.xcscmblueprint
*.xcuserstate

# Xcode build
DerivedData/
build/
*.xcworkspace
*.xcodeproj
*.xcuserstate

# Others
*.log
.angular/cache/