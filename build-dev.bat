@echo off
echo Starting development build process...

echo Step 1: Installing/updating dependencies...
npm install

echo Step 2: Ensuring electron platform is ready...
call ionic cordova platform add electron

echo Step 3: Preparing electron platform...
call ionic cordova prepare electron

echo Step 4: Fixing missing node modules in electron platform...
cd platforms\electron\www
if exist node_modules\semver rmdir /s /q node_modules\semver
npm install semver@latest

if exist node_modules\jszip rmdir /s /q node_modules\jszip
npm install jszip@latest

cd ..\..\..

echo Step 5: Installing missing Cordova plugins...  
cd platforms\electron\www
npm install ..\..\..\plugins\cordova-plugin-unvired-logger\src\electron
npm install ..\..\..\plugins\cordova-plugin-unvired-device\src\electron
npm install ..\..\..\plugins\cordova-plugin-unvired-electron-db\src\electron
npm install ..\..\..\plugins\cordova-plugin-unvired-file\src\electron
cd ..\..\..

echo Step 6: Building and running development version...
call ionic cordova run electron

echo Development build process completed!
