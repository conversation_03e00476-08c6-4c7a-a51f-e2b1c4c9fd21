@echo off
echo Starting production build process...
echo Checking node version...
node -v

echo Step 1: Cleaning previous builds...
if exist platforms\electron\build rmdir /s /q platforms\electron\build
if exist www rmdir /s /q www

echo Step 2: Installing/updating dependencies...
npm install

echo Step 3: Ensuring electron platform is ready...
call ionic cordova platform add electron

echo Step 4: Preparing electron platform...
call ionic cordova prepare electron

echo Step 5: Fixing missing node modules in electron platform...
cd platforms\electron\www
if exist node_modules\semver rmdir /s /q node_modules\semver
npm install semver@latest

if exist node_modules\jszip rmdir /s /q node_modules\jszip
npm install jszip@latest

cd ..\..\..

echo Step 6: Installing missing Cordova plugins...
cd platforms\electron\www
npm install ..\..\..\plugins\cordova-plugin-unvired-logger\src\electron
npm install ..\..\..\plugins\cordova-plugin-unvired-device\src\electron
npm install ..\..\..\plugins\cordova-plugin-unvired-electron-db\src\electron
npm install ..\..\..\plugins\cordova-plugin-unvired-file\src\electron
cd ..\..\..

echo Step 7: Ensuring DB plugin is in dependencies...
cd platforms\electron\www
npm install
cd ..\..\..

echo Step 8: Building production release...
call ionic cordova build electron --release --prod

echo Step 9: Checking build output...
if exist platforms\electron\build\win-unpacked (
    echo Production build completed successfully!
    echo Build location: platforms\electron\build\win-unpacked
    if exist "platforms\electron\build\PD-FORMS*.msi" (
        echo Installer created: platforms\electron\build\PD-FORMS.msi
    )
) else (
    echo Build failed - output directory not found
    exit /b 1
)

echo Production build process completed!
pause
