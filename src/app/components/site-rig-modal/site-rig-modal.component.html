<ion-header>
  <ion-toolbar color="primary">
    <ion-title>Enter Site Number</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <!-- <ion-item lines="none">
    <ion-label>Enter your site </ion-label>
  </ion-item> -->
  <ion-item>
    
    <ion-input placeholder="Enter Site number" [(ngModel)]="siteNumber" type="text"></ion-input>
  </ion-item>
  <div *ngIf="errorMessage" style="color: red; margin-top: 8px;">
  * {{ errorMessage }}
</div>

  <div >
    <ion-button expand="block" fill="solid" color="primary" (click)="submit()">
      Switch site
    </ion-button>
  </div>
<!-- <div *ngIf="rigData$ | async as rig;">
  <p>{{ rig.RIG_NO }}</p>
</div> -->



</ion-content>


