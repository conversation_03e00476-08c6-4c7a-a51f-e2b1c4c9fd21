:host {
  ion-content {
    height: 100vh; // critical fix
    --background: url('../../assets/imgs/login_background.jpg') no-repeat center center / cover;
  }

  .vertical-align-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .center-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  .overlay {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 400px;
    max-width: 90%;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .language-select-container {

    display: flex;
    justify-content: center;

    ion-select {
      width: 100%;
      max-width: 200px;
    }
  }

  .custom-button-group {
  display: flex;
  flex-direction: row;     // horizontal layout
  align-items: center;
  justify-content: center;
  width: fit-content;
  background: transparent;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  gap: 0px;  // or add gap if needed
}

.button-text{
  color: white
}

.server-selection-button{
  color: white;
  border-radius: 0%;
}

    .footer-right {
        font-size: small;
        color: #FFF;
        white-space: nowrap;
        position: absolute;
        bottom: 5px;
        right: 20px;
    }





}

ion-modal.example-modal {
  --width: fit-content;
  --min-width: 250px;
  --height: fit-content;
  --border-radius: 6px;
  --box-shadow: 0 28px 48px rgba(0, 0, 0, 0.4);
}

.example-modal h1 {
  margin: 20px 20px 10px 20px;
}

.example-modal ion-icon {
  margin-right: 6px;

  width: 48px;
  height: 48px;

  padding: 4px 0;

  color: #aaaaaa;
}

.example-modal .wrapper {
  margin-bottom: 10px;
}
