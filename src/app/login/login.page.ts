import { Component, <PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AlertController, LoadingController, MenuController, NavController, NavParams, PopoverController, ToastController } from '@ionic/angular';
import { addIcons } from 'ionicons';
import { home, lockClosedOutline, caretUpOutline } from 'ionicons/icons';
import { AppConstants } from '../constants/appConstants';
import { UtilityService } from '../services/utility.service';
import { AlertService } from '../services/alert.service';
import { BusyIndicatorService } from '../services/busy-indicator.service';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { IonButton, IonLabel, IonSelect , IonContent , IonModal, IonSelectOption, IonList, IonItem, Platform    } from '@ionic/angular/standalone';
import { AuthenticateActivateResult,AuthenticateLocalResultType , AuthenticateAndActivateResultType, AuthenticateLocalResult, LoginListenerType, LoginParameters, LoginType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { Deeplinks } from '@awesome-cordova-plugins/deeplinks/ngx';
import { SplashScreen } from '@awesome-cordova-plugins/splash-screen/ngx';
import { Store } from '@ngrx/store';
import * as RigActions from 'src/app/store/store.actions';
import { DataService } from '../services/data.service';
@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
  standalone: true,
  imports: [IonSelect, IonButton , IonLabel , IonSelectOption, CommonModule, FormsModule , IonContent , IonModal , IonList , IonItem],
  providers: [PopoverController, Deeplinks,]
})
export class LoginPage implements OnInit {
  @ViewChild('modal') modal!: IonModal;
  isModalOpen = false;
  umpUrls = AppConstants.LOGIN_UMP_URLS;
  selectedUrl = '';
  modalClass = 'example-modal'


   loginParameters: LoginParameters | null = null;
  samlLoginParams: any = null;

 url: any;

  loading: any;

  version = AppConstants.RELEASE_DATE + '' + AppConstants.RELEASE_NUMBER


  public versionHide: boolean = true;
  public language: any;
  public devicePlatform = 'browser';
  public busyIndicator: any = null;
  public emailId: string = '';
  public password: string = '';
  // public isDevice: boolean =false;

  // public url: string = null;
  public serverUrls: any = [];
  public loginResultType: LoginListenerType | null = null;

  // public showErrorMsg: boolean = false;
  // public loginErrMsg: string = '';
  public company: string = '';
  public errorMessage: string = '';
  public showErrorMessage: boolean = false;
  public messageColor: string = '';
  public progressbar: boolean = false;
  public devicetype = '';

  constructor( public toastCtrl: ToastController,
    private store: Store,
    public menu: MenuController,
    public alertCtrl: AlertController,
    public utilityService: UtilityService,
  public alertService: AlertService,
    public popoverCtrl: PopoverController,
    public alertController: AlertController,
    private platform: Platform,
        public splashScreen: SplashScreen,
        public navCtrl: NavController,
        private router: Router,
    private ngZone: NgZone,
    private translate: TranslateService,
      private unviredSDK: UnviredCordovaSDK,
      private loadingController: LoadingController,
         private deeplinks: Deeplinks,
         private busyIndicatorService: BusyIndicatorService,
         private dataService: DataService
 ) {
    addIcons({
      home,
      'lock-closed-outline': lockClosedOutline,
      'caret-up-outline': caretUpOutline
    });

        this.translate.setDefaultLang('en');
     const lang = localStorage.getItem('lang') || 'en';
  this.utilityService.changeLanguage(lang);
  }

  async ngOnInit() {
       this.unviredSDK.logInfo('login page', 'ngOnInit', 'test login page' )
    await this.platform.ready()

    console.log('NgOnit Login')
   const savedLang = localStorage.getItem('lang') || 'en';
  this.utilityService.changeLanguage(savedLang);
    
   
  if (this.platform.is('electron')) {
    this.devicetype = 'WINDOWS';

      (window as any).electronAPI?.onDeepLink(async (url: string) => {
    const token = new URL(url).searchParams.get('token');
    if (token) {
      console.log('Received SSO token:', token);
      await this.forelectron(token);
    } else {
      console.error('No token found in the URL');
    }
  
  })
  } else if (this.platform.is('ios')) {
    this.samlLoginParams = {
      appName: AppConstants.APPLICATION_NAME,
      metadataPath: 'assets/metadata.json',
      url: this.url,
      company: AppConstants.COMPANY_NAME,
      loginType: 'SAML2',
      autoSyncTime : '5'
    };
    this.devicetype = this.platform.is('ipad') ? 'IPAD' : 'IPHONE';
      this.checkAuthentication();
    console.log("ready")
        this.deeplinks.route({
            '/callback': 'callback'  // Maps forms://callback
        }).subscribe((match: any) => {

            console.log('Deeplink matched:', match);

            if (match.$args?.error) {
    // Decode URI-encoded error message
    const errorMsg = decodeURIComponent(match.$args.error);
    this.busyIndicatorService.presentAlert(errorMsg);  // Show popup (your own method)
    return; // Stop further execution
  }

   
    console.log('the login params', this.samlLoginParams)
         
           if(match.$args.token != undefined){
            this.busyIndicatorService.displayBusyIndicator('Logging in...');
            this.authenticate(match.$args.token)
           }else{
            console.log('navigating to logins')
            // this.navigateToLogin();
           }
            
            // this.router.navigateByUrl('/your-callback-page');  // Navigate as needed
        }, (nomatch) => {
            console.warn('No match found:', nomatch);
        });
    };
  
  console.log('Device Type:', this.devicetype);

}

  async openModal() {
    this.isModalOpen = true;
    if (this.modal) {
      await this.modal.present();
    }
  }

  async saveUrl(url: string) {
    // console.log(this.umpUrls)
    this.selectedUrl = url;
 
    this.isModalOpen = false;
    this.onSSOLoginButtonClicked();
    if (this.modal) {
      await this.modal.dismiss(url);
    }
  }

  cancel() {
    this.isModalOpen = false;
    if (this.modal) {
      this.modal.dismiss();
    }
  }



  onSSOLoginButtonClicked() {
    // this.router.navigate(['/templates'])
    // this.router.navigate(['/ngrx-counter'])
    // Handle SSO login
      console.log('Initiating SSO login');
      console.log('selected is',this.selectedUrl)
      if(this.selectedUrl == ''){
         this.url = 'https://ump.pd.com:8443/UMP'
      }else{
          this.url = this.selectedUrl
      }
    
    const baseUrl = this.url ;
    console.log('Base URL:', baseUrl);
    // this.devicetype =''
   const redirectUrl = 'forms://callback';
    const url = `${baseUrl}/sso/saml/dologin?company=PD&application=${AppConstants.APPLICATION_NAME}&device=${this.devicetype}&redirect=${encodeURIComponent(redirectUrl)}`;
    console.log('Redirecting to the:', url);
    
    if (this.platform.is('electron')) {
      if (window.electronAPI && window.electronAPI.openExternal) {
        window.electronAPI.openExternal(url);
      } else {
        console.error('Electron API not available');
      }
    } else {
     
      window.location.href = url;
    console.log(window.location.href)
    }
  }


   async initializeApp() {
    await this.platform.ready();

    setTimeout(() => {
      this.splashScreen.hide();  
    }, 5000); 

    const splash = document.getElementById('splash-screen');
    if (splash) splash.style.display = 'none';

    
    // this.loginParameters = {
    //   // appVersion: AppConstants.VERSION_NO,
    //   appName: AppConstants.APPLICATION_NAME,
    //   metadataPath: 'assets/metadata.json',
    //   url: this.url,
    //   company: AppConstants.COMPANY_NAME,
    //   loginType: 'SAML2',
    //   // umpRESTapiVersion: AppConstants.UMP_REST_API_VERSION,
    // };

    // this.checkAuthentication();
    
  }
  
  async checkAuthentication() {
     this.unviredSDK.logInfo('login page', 'checkAuth in login', " LOGIN PARAMS: " + JSON.stringify(this.loginParameters) )
    console.log('Checking authentication...');
    console.log('samlLoginParams:', this.samlLoginParams , LoginListenerType);
    try {
      const loginResult = await this.unviredSDK.login(this.samlLoginParams!);
      console.log('Login result:', loginResult);
      switch (loginResult.type) {
        case LoginListenerType.auth_activation_required:
          console.log('Auth activation required');
          this.busyIndicatorService.hideBusyIndicator();
          const tokenResponse = this.getToken(window.location.href);
          if (tokenResponse && tokenResponse.token) {
            console.log('Token found, authenticating...');
            this.ngZone.run(() => {
              this.busyIndicatorService.displayBusyIndicator('Logging in...');
              this.authenticate(tokenResponse.token!);
            });
          } else {
            console.error('No token found, navigating to login.');
            this.navigateToLogin();
          }
          break;

        case LoginListenerType.login_success:
          console.log('Login successful');
         this.busyIndicatorService.hideBusyIndicator();
          this.selectedUrl = '';
          this.navigateToHome();
          break;

        default:
          console.log('Unexpected login result type in login page:', loginResult.type);
          this.busyIndicatorService.hideBusyIndicator();
          this.navigateToLogin();
          break;
      }
    } catch (error: any) {
      console.error('Login failed:', error);
      this.busyIndicatorService.hideBusyIndicator();
      this.navigateToLogin();
    }
  }

   getToken(url: string): { token?: string; error?: string } {
    const params = new URLSearchParams(new URL(url).search);
    if (params.has('token')) {
      return { token: params.get('token')! };
    }
    return { error: 'Token not found in URL.' };
  }


   async authenticate(token: string) {
    console.log('Authenticating with token:', token);
    
     console.log('SAML Login Params:', this.selectedUrl , this.url , this.samlLoginParams);
   
    this.samlLoginParams.jwtToken = token;
    if(this.selectedUrl == ''){
      this.samlLoginParams.url = 'https://ump.pd.com:8443/UMP';
    }else{
      this.samlLoginParams.url  = this.selectedUrl
    }
    
    try {
      const authResult = await this.unviredSDK.authenticateAndActivate(this.samlLoginParams);
      
      console.log('Authentication result:', authResult);
      if (authResult.type === AuthenticateAndActivateResultType.auth_activation_success) {
       this.busyIndicatorService.hideBusyIndicator();
        console.log('Authentication successful, navigating to home page');
        this.navigateToHome();
      } else {
       this.busyIndicatorService.hideBusyIndicator();
        this.showErrorAlert('Authentication Error', authResult.error || 'An unknown error occurred.');
        this.navigateToLogin();
      }
    } catch (error: any) {
     this.busyIndicatorService.hideBusyIndicator();
      this.showErrorAlert('Authentication Error', error?.message || 'An unknown error occurred.');
      this.navigateToLogin();
    }
  }

  private navigateToHome() {
    
    this.store.dispatch(RigActions.loadPrefilledData());
    this.unviredSDK.userSettings().then((result: any) => {
      const data = result.data || {};
      this.dataService.selectedServer = data.SERVER_TYPE
    });
    this.ngZone.run(() => {
      console.log('Navigating to home page');
      this.router.navigate(['/forms']);
    });
  }

  private navigateToLogin() {
    this.ngZone.run(() => {
      this.router.navigate(['/login']);
    });
  }
  async showErrorAlert(title: string, message: string) {
    const alert = await this.alertController.create({
      header: title,
      message,
      buttons: [this.translate.instant('OK')],
    });
    await alert.present();
  }

  async presentLoading(message: string) {
    this.loading = await this.loadingController.create({
      message,
    });
    await this.loading.present();
  }

  
  async displayBusyIndicator(message: string) {
    this.loading = await this.loadingController.create({ message });
    await this.loading.present();
  }



  async showConfirmationAlert(title: string, message: string, onConfirm: () => void) {
    const alert = await this.alertController.create({
      header: title,
      message,
      buttons: [
        {
          text: this.translate.instant('Logout'),
          handler: onConfirm,
        },
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
        },
      ],
    });
    await alert.present();
  }


  forelectron(token: string) {
    this.busyIndicatorService.displayBusyIndicator('Logging in...');
   this.samlLoginParams = {
      // appVersion: AppConstants.VERSION_NO,    
      appName: AppConstants.APPLICATION_NAME,
      metadataPath: 'assets/metadata.json',
      url: `https://ump.pd.com:8443`,
      company: AppConstants.COMPANY_NAME,
      loginType: 'SAML2',
      // autoSyncTime : '5'
      // umpRESTapiVersion: AppConstants.UMP_REST_API_VERSION,
    };
    this.authenticate(token);
    // console.log('authenticated')
  }


  async unviredLogin() {

     //   this.emailId = '<EMAIL>';
    // this.password = 'Unvired321**'
 
    this.emailId = '<EMAIL>';
    this.password = 'Unvired123*'
    console.log('the email and password is ' , this.emailId , this.password)
    this.language = localStorage.getItem('lang') || 'en';
    // Busy indicator, when other processes are running
    this.busyIndicator = await this.loadingController.create({
      message: this.translate.instant('Signing in...'),
      spinner: this.translate.instant('crescent'),
      animated: true,
      showBackdrop: true,
      translucent: true,
    });

      // Set the arguments for ump
      this.loginParameters = new LoginParameters();
      this.loginParameters.url = `https://umpdev.pd.com:8443`;
      this.loginParameters.company = 'PD';
      this.loginParameters.loginType = LoginType.email;
      this.loginParameters.username = this.emailId.trim();
      this.loginParameters.password = this.password;
      this.loginParameters.jwtOptions = {
        app: 'PD_FORMS',
        language: this.language,
      };
      this.loginParameters.appName = AppConstants.APPLICATION_NAME;
      this.loginParameters.metadataPath ='assets/metadata.json',
      this.loginParameters.cacheWebData = true;
      this.loginParameters.autoSyncTime = '5';

      // send selected language.
      this.loginParameters.loginLanguage = this.language;
      this.unviredSDK.logInfo(
        'Login',
        'login()',
        'Selected Language Code: ' + this.language
      );
       this.unviredSDK.logInfo('login page', 'unviredlogin', "APP LOGIN PARAMS: " + JSON.stringify(this.loginParameters) )
      let loginResult = await this.unviredSDK.login(this.loginParameters);
      this.loginResultType = loginResult.type;
      console.log('this.loginResultType iss' , this.loginResultType)
      try {
        switch (this.loginResultType ) {
        
          // |Authenticate and Activate|
          case LoginListenerType.auth_activation_required:
            await this.busyIndicator.present();
            let authenticateActivateResult: AuthenticateActivateResult | null = null;
          
            try {
              try {
                authenticateActivateResult = await this.unviredSDK.authenticateAndActivate(
                  this.loginParameters
                );
                // |Authentication activated and Login success|
              } catch (err) {
                console.log("error is", err);
              }
          
              if (
                authenticateActivateResult &&
                authenticateActivateResult.type === AuthenticateAndActivateResultType.auth_activation_success
              ) {
                this.busyIndicator.dismiss();
                this.displayLandingPage();
                this.emailId = '';
                this.password = '';
              } else if (
                authenticateActivateResult &&
                authenticateActivateResult.type === AuthenticateAndActivateResultType.auth_activation_error
              ) {
                this.busyIndicator.dismiss();
                this.showErrorMessage = true;
                this.errorMessage = authenticateActivateResult.error;
                this.messageColor = 'danger';
              }
            } catch (error) {
              this.unviredSDK.logError(
                'LoginPage',
                'authenticateAndActivate()',
                'ERROR: ' + error
              );
            }
            break;
          
          // |Authenticate Locally|
          case LoginListenerType.app_requires_login:
            this.busyIndicator.present();
            let authenticateLocalResult: AuthenticateLocalResult;
            try {
              authenticateLocalResult = await this.unviredSDK.authenticateLocal(
                this.loginParameters
              );
              // |Authenticate (Local) credentials saved in database|

              if (
                authenticateLocalResult.type ===
                AuthenticateLocalResultType.login_success
              ) {
                // this.setLocalDataToStorage();
                this.busyIndicator.dismiss();

                this.displayLandingPage();
                this.emailId = '';
                this.password = '';
              } else if (
                authenticateLocalResult.type ===
                AuthenticateLocalResultType.login_error
              ) {
                this.busyIndicator.dismiss();
                this.busyIndicatorService.presentAlert(authenticateLocalResult.error);
              }
            } catch (error) {
              this.unviredSDK.logError(
                'LoginPage',
                'authenticateLocal()',
                'ERROR: ' + error
              );
            }
            break;
       
          case LoginListenerType.login_success:
              // Srinidhi: 4 Oct 2022: Set this flag so that when the users navigate to the home page, then it starts downloading customization data.
              // this.isStartCustomization = true;
              this.displayLandingPage();
              break;
           }
      } catch (error) {
        this.unviredSDK.logError('LoginPage', 'login()', 'ERROR: ' + error);
      }
 
  }

  // Navigate to landing page
  displayLandingPage() {
    // Flag for do customization only when user navigates from login page.
    // this.dataService.isStartCustomization = true;
    this.router.navigate(['templates']);
  }

  // Alert, when get any error response from ump


}


