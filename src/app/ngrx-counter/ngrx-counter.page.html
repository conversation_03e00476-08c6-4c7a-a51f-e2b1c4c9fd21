<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>
      Blank
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Blank</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- <div id="container">
    <strong>Ready to create an app.........?</strong>
    <p>Start with Ionic <a target="_blank" rel="noopener noreferrer" href="https://ionicframework.com/docs/components">UI Components</a></p>
  </div> -->

  <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 40vh;">
    <h2>Counter: {{ counter$ | async }}</h2>
    <div style="display: flex; gap: 16px; margin-top: 16px;">
      <ion-button (click)="decrementCount()">Decrement</ion-button>
      <ion-button (click)="resetCount()">Reset</ion-button>
      <ion-button (click)="incrementCount()">Increment</ion-button>
       <ion-button (click)="incrementAsync()">Increment Async</ion-button>
    </div>
  </div>
</ion-content>
