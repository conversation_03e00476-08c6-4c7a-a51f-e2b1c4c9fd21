import { Component } from '@angular/core';
import { IonHeader, IonToolbar, IonTitle, IonContent, IonButton } from '@ionic/angular/standalone';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { increment ,decrement , reset, incrementAsync } from '../store/store.actions';
import { CommonModule } from '@angular/common';
import { selectCount } from '../store/store.selector';
import { AppState } from '../store/app.state';
@Component({
  selector: 'app-ngrx-counter',
  templateUrl: 'ngrx-counter.page.html',
  styleUrls: ['ngrx-counter.page.scss'],
  imports: [IonHeader, IonToolbar, IonTitle, IonContent, IonButton , CommonModule],
})
export class ngrxCounter {
  counter$: Observable<number>;

  constructor(private store: Store<AppState>) {


    this.counter$ = this.store.select(selectCount);

  }

  incrementCount() {
    this.store.dispatch(increment())
  }

  decrementCount() {
    this.store.dispatch(decrement())
  }

  resetCount() {
    this.store.dispatch(reset())
  }

  incrementAsync() {
    this.store.dispatch(incrementAsync());
  }

}
