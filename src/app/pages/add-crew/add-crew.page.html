<ion-header class="add-crew-header">
  <ion-toolbar color="primary" class="add-crew-toolbar">
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="closeModal()">
        <ion-icon name="arrow-back" style="color: white;"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title class="add-crew-title">{{ 'Edit Temporary Crew Members' | translate }}</ion-title>
    <ion-buttons slot="end">
      <ion-button
        id="addCrewButton"
        fill="clear"
        (click)="addCrewMember()"
        class="add-button">
        <ion-icon name="add-circle" style="font-size: 22px;"></ion-icon>
      </ion-button>
      <ion-button
        id="saveButton"
        fill="clear"
        (click)="saveCrewMember()"
        class="save-button">
        {{ 'Save' | translate }}
      </ion-button>

    </ion-buttons>
  </ion-toolbar>

  <!-- Table Header -->
  <div class="add-crew-table-header">
    <div class="header-row">
      <div class="header-col serial-col">#</div>
      <div class="header-col name-col">{{ 'NAME' | translate }}</div>
      <div class="header-col position-col">{{ 'POSITION / COMPANY' | translate }}</div>
      <div class="header-col action-col">{{ 'Action' | translate }}</div>
    </div>
  </div>
</ion-header>



<ion-content class="add-crew-content" [ngClass]="{ contrast: theme === 'contrast' }">
  <!-- Crew Members List -->
  <div class="crew-cards-container" *ngIf="crewMembers().length > 0">
    <ion-card
      *ngFor="let crewMember of crewMembers(); let i = index"
      class="crew-member-card"
      [ngClass]="{
        'has-unsaved-name': crewMember.userNameUnsaved,
        'has-unsaved-designation': crewMember.designationUnsaved,
        'crew-distinct': crewMember.crewIdDistinct
      }">
      <ion-card-content class="crew-card-content">
        <ion-row class="crew-member-row" align-items-center>
          <!-- Serial Number -->
          <ion-col size="1" class="serial-col">
            <span class="serial-number">{{ i + 1 }}</span>
          </ion-col>

          <!-- Name Input -->
          <ion-col size="5" class="name-input-col">
            <ion-input
              type="text"
              maxlength="50"
              placeholder="{{ 'Enter Username' | translate }}"
              [(ngModel)]="crewMember.USER_NAME"
              (ionInput)="onChangeInput($event, 'userName', crewMember)"
              class="crew-input"
              [ngClass]="{'has-unsaved': crewMember.userNameUnsaved}"
              fill="outline">
            </ion-input>
          </ion-col>

          <!-- Position Input -->
          <ion-col size="5" class="position-input-col">
            <ion-input
              type="text"
              maxlength="50"
              placeholder="{{ 'Enter Position / Designation' | translate }}"
              [(ngModel)]="crewMember.DESIGNATION"
              (ionInput)="onChangeInput($event, 'designation', crewMember)"
              class="crew-input"
              [ngClass]="{'has-unsaved': crewMember.designationUnsaved}"
              fill="outline">
            </ion-input>
          </ion-col>

          <!-- Remove Button -->
          <ion-col size="1" class="action-col">
            <ion-button
              fill="clear"
              size="small"
              (click)="removeNewEntry(crewMember)"
              class="remove-button">
              <ion-icon name="close" color="danger"></ion-icon>
            </ion-button>
          </ion-col>
        </ion-row>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- No Crew Members Message -->
  <div class="no-crew-message" *ngIf="crewMembers().length === 0">
    <p>{{ 'No Crew Members Available.' | translate }}</p>
  </div>
</ion-content>
