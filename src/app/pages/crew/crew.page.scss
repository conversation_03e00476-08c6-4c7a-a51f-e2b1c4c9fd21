/* Green header */
.crew-header {
  --background: #4CAF50 !important;
}

.crew-header .crew-toolbar {
  --background: #4CAF50 !important;
  --color: #fff !important;
}

.crew-header .crew-toolbar .crew-title {
  color: #fff !important;
  font-weight: 500;
  font-size: 1.2rem;
  line-height: 1;
}

.crew-header .crew-toolbar .save-button,
.crew-header .crew-toolbar .edit-crew-button {
  --color: #fff !important;
  font-weight: 500;
  font-size: 0.9rem;
  line-height: 1;
}

/* Search container */
.search-container {
  background: #4CAF50;
  padding: 8px 16px;
}

.search-container .crew-searchbar {
  --background: #fff;
  --border-radius: 4px;
  --box-shadow: none;
  margin: 0;
}



/* Content */
.crew-content {
  --background: #f5f5f5;
  padding: 8px;
}

.crew-content .crew-list .crew-card {
  margin: 8px 0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.crew-content .crew-list .crew-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.crew-content .crew-list .crew-card.selected-card {
  border-color: #4CAF50;
  background: #f0f8f0;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
}

.crew-content .crew-list .crew-card.has-error {
  border-color: #d32f2f;
}

.crew-content .crew-list .crew-card .crew-card-content {
  position: relative;
  padding: 16px;
}

.crew-content .crew-list .crew-card .crew-card-content .checkbox-container {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
}

.crew-content .crew-list .crew-card .crew-card-content .checkbox-container .crew-checkbox {
  --size: 20px;
  --checkmark-color: #fff;
  --background-checked: #4CAF50;
  --border-color-checked: #4CAF50;
  --border-color: #ccc;
  --border-width: 2px;
}

.crew-content .crew-list .crew-card .crew-card-content .crew-details {
  padding-right: 40px; /* Space for checkbox */
}

.crew-content .crew-list .crew-card .crew-card-content .crew-details .crew-field {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  min-height: 20px;
}

.crew-content .crew-list .crew-card .crew-card-content .crew-details .crew-field:last-child {
  margin-bottom: 0;
}

.crew-content .crew-list .crew-card .crew-card-content .crew-details .crew-field .field-label {
  font-weight: 600;
  color: #666;
  min-width: 100px;
  flex-shrink: 0;
  font-size: 0.875rem;
  line-height: 1.4;
}

.crew-content .crew-list .crew-card .crew-card-content .crew-details .crew-field .field-value {
  font-weight: 400;
  color: #333;
  flex: 1;
  font-size: 0.875rem;
  line-height: 1.4;
  word-break: break-word;
  margin-left: 8px;
}

.crew-content .crew-list .crew-card .crew-card-content .crew-details .crew-field .field-value.mandatory-text {
  color: #d32f2f;
}

/* Mandatory selection */
.mandatory-text {
  color: #d32f2f !important;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-state p {
  font-size: 1.1rem;
  margin: 0;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .crew-content {
    padding: 4px;
  }

  .crew-card {
    margin: 6px 0 !important;
  }

  .crew-card .crew-card-content {
    padding: 12px;
  }

  .crew-card .crew-card-content .checkbox-container {
    top: 8px;
    right: 8px;
  }

  .crew-card .crew-card-content .checkbox-container .crew-checkbox {
    --size: 18px;
  }

  .crew-card .crew-card-content .crew-details {
    padding-right: 35px;
  }

  .crew-card .crew-card-content .crew-details .crew-field {
    margin-bottom: 6px;
  }

  .crew-card .crew-card-content .crew-details .crew-field .field-label {
    min-width: 80px;
    font-size: 0.8rem;
  }

  .crew-card .crew-card-content .crew-details .crew-field .field-value {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .crew-content {
    padding: 2px;
  }

  .crew-card {
    margin: 4px 0 !important;
  }

  .crew-card .crew-card-content {
    padding: 10px;
  }

  .crew-card .crew-card-content .crew-details {
    padding-right: 30px;
  }

  .crew-card .crew-card-content .crew-details .crew-field .field-label {
    min-width: 70px;
    font-size: 0.75rem;
  }

  .crew-card .crew-card-content .crew-details .crew-field .field-value {
    font-size: 0.75rem;
  }
}
