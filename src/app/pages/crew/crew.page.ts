import { Component, <PERSON>Z<PERSON>, OnInit, Input } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MenuController } from '@ionic/angular';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Store } from '@ngrx/store';
import { triggerPrefillRefresh } from 'src/app/store/store.actions';

import { AppConstants } from '../../constants/appConstants';
import { AlertService } from 'src/app/services/alert.service';
import { UtilityService } from 'src/app/services/utility.service';
import { CREW_HEADER } from 'src/models/CREW_HEADER';

import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { IonButton, IonIcon, IonButtons, IonCheckbox, IonCol, IonContent, IonGrid, IonHeader, IonItem, IonLabel, IonList, IonRow, IonSearchbar, IonTitle, IonToolbar, IonCard, IonCardContent, ModalController } from '@ionic/angular/standalone';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { AddCrewPage } from '../add-crew/add-crew.page';
import { addIcons } from 'ionicons';
import { arrowBackOutline } from 'ionicons/icons';

addIcons({
  arrowBackOutline
});

@Component({
  selector: 'app-crew',
  templateUrl: './crew.page.html',
  styleUrls: ['./crew.page.scss'],
  standalone: true,
  imports: [IonHeader, IonToolbar, IonTitle, IonButtons, IonList, IonButton, IonGrid, IonSearchbar, IonItem, IonLabel, IonContent, IonCheckbox, IonCard, IonCardContent, FormsModule, CommonModule, TranslateModule, IonRow, IonCol, IonIcon]
})
export class CrewPage implements OnInit {
  filter: { searchText: string } = { searchText: '' };
  crewMembers: CREW_HEADER[] = [];
  crewMembersCopy: CREW_HEADER[] = [];
  displayCrewMembers: CREW_HEADER[] = [];

  // Input properties for modal data
  @Input() crewData: any;
  @Input() theme: string = 'normal';
  @Input() RIG_NO: string = '';

  STMRDetailsPage: any;
  styleTheme: string = 'normal';

  hasCrew: boolean = AppConstants.BOOL_FALSE;
  stmrCrewIdAndName: Array<{ Id: string; Name: string }> = [];
  selPersonNo: string[] = [];

  constructor(
    private translate: TranslateService,
    private router: Router,
    private route: ActivatedRoute,
    private alertService: AlertService,
    private menu: MenuController,
    private ngZone: NgZone,
    private utilityService: UtilityService,
    private unviredSDK: UnviredCordovaSDK,
    private modalCtrl: ModalController,
    private store: Store
  ) {}

  ngOnInit() {
    // Use Input properties from modal componentProps, fallback to history.state for navigation
    const navData: any = history.state;
    this.STMRDetailsPage = navData?.STMRDetailsPage;

    // Prioritize Input properties (from modal) over navigation state
    this.styleTheme = this.theme || navData?.theme || 'normal';
    this.crewData = this.crewData || navData?.data?.crew || [];
    this.RIG_NO = this.RIG_NO || navData?.data?.RIG_NO || '';

    console.log('[CrewPage] ngOnInit - crewData:', this.crewData);
    console.log('[CrewPage] ngOnInit - theme:', this.styleTheme);
    console.log('[CrewPage] ngOnInit - RIG_NO:', this.RIG_NO);

    this.getCrewMembersFromDB(true);
  }

  ionViewWillEnter() {
    this.ngZone.run(() => {
      this.menu.enable(AppConstants.BOOL_FALSE);
      this.menu.swipeGesture(AppConstants.BOOL_FALSE);
    });
  }

private async getCrewMembersFromDB(isFirst?: boolean) {
  try {
    const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_CREW_HEADER, {});
    console.log('[CrewPage] DB Crew Members:', result.data);

    if (result.data?.length > 0) {
      // 🔹 Map DB rows into a uniform shape expected by the UI
      this.crewMembers = result.data.map((row: any, index: number) => ({
        ...row,
        USER_NAME: row.USER_NAME || row.NAME || `Crew ${index + 1}`,
        DESIGNATION: row.DESIGNATION || row.POSITION || '',
        USER_ID: row.USER_ID || row.LID || '',
        GLOBALID: row.GLOBALID || '',
        PERSON_NO: row.PERSON_NO || row.LID || '',
        SOURCE: row.SOURCE || 'LOCAL',
        index: 'crew-' + (index + 1),
        isSelected: AppConstants.BOOL_FALSE
      }));

      // 🔹 If crewData was passed in nav params, mark them as selected
      if (this.crewData?.length) {
        // Clear existing selections first
        this.selPersonNo = [];

        this.crewData.forEach((crew: any) => {
          // Check if this crew member exists in the available crew list and is not deleted
          if (crew.P_MODE !== 'D') {
            const foundMember = this.crewMembers.find(member =>
              member.PERSON_NO === crew.CREW_ID
            );
            if (foundMember && !this.selPersonNo.includes(crew.CREW_ID)) {
              this.selPersonNo.push(crew.CREW_ID);
            }
          }
        });
      }

      // 🔹 Apply selection state
      this.crewMembers.forEach(member => {
        const memberId = member.SOURCE === 'LOCAL' ? member.USER_NAME : member.PERSON_NO;
        member.isSelected = this.selPersonNo.includes(memberId);
      });

      this.crewMembersCopy = JSON.parse(JSON.stringify(this.crewMembers));
      this.displayCrewMembers = JSON.parse(JSON.stringify(this.crewMembers));

      this.filterList(this.filter.searchText);
      this.hasCrew = this.noCrewSelected();

      console.log('[CrewPage] Final displayCrewMembers:', this.displayCrewMembers);
      console.log('[CrewPage] Selected crew IDs:', this.selPersonNo);
      console.log('[CrewPage] Has any selection:', this.hasAnySelection());
    }
  } catch (error) {
    this.unviredSDK.logError(
      'CrewPage',
      'getCrewMembersFromDB',
      `Error while fetching Crew Members: ${JSON.stringify(error)}`
    );
    this.alertService.showAlert('Error', JSON.stringify(error));
  }
}



  toggleSelectCrew(crew: CREW_HEADER) {
    console.log('crewww ' , crew)
    // Use USER_NAME for local crew members, PERSON_NO for others
    const crewId = crew.SOURCE === 'LOCAL' ? crew.USER_NAME : crew.PERSON_NO;

    if (crew.isSelected) {
      if (!this.selPersonNo.includes(crewId)) {
        this.selPersonNo.push(crewId);
      }
    } else {
      this.selPersonNo = this.selPersonNo.filter(id => id !== crewId);
    }

    // Update the main crew members array to reflect the change
    const mainCrewMember = this.crewMembers.find(m =>
      m.SOURCE === 'LOCAL' ? m.USER_NAME === crew.USER_NAME : m.PERSON_NO === crew.PERSON_NO
    );
    if (mainCrewMember) {
      mainCrewMember.isSelected = crew.isSelected;
    }

    this.ngZone.run(() => {
      this.hasCrew = this.noCrewSelected();
    });

    console.log('[CrewPage] toggleSelectCrew - Selected crew IDs:', this.selPersonNo);
  }

  saveCrewMember() {
  const selected = this.crewMembers.filter(m => m.isSelected);

  const mappedCrew = selected.map(m => ({
    CREW_ID: m.SOURCE === 'LOCAL' ? m.USER_NAME : m.PERSON_NO,
    CREW_NAME: m.USER_NAME,
    CREW_POS: m.DESIGNATION,
    CREW_TYPE: 'INTERNAL',
    P_MODE: '',
    CREW_SIGN: null
  }));

  console.log('[CrewPage] Saving selected crew:', mappedCrew);

  // Return all selected crew members to STMR Details page
  this.modalCtrl.dismiss({ selectedCrew: mappedCrew }, 'confirm');
}



  async addCrewMember() {
  const modal = await this.modalCtrl.create({
    component: AddCrewPage,
    componentProps: {
      data: { RIG_NO: this.RIG_NO },
      crewPage: this,
      selectedCrew: this.crewMembers,
      theme: this.styleTheme
    },
    cssClass: 'full-screen-modal'  // ✅ custom full screen
  });

  await modal.present();

  // Handle data returned from modal when dismissed
  const { data, role } = await modal.onWillDismiss();
  console.log('[CrewPage] Modal dismissed with role:', role, 'data:', data);

  if (role === 'confirm' && data) {
    console.log('[CrewPage] Updating crew members from modal data');
    // Update crew members or do something with returned data
    this.crewMembers = data.updatedCrew || this.crewMembers;

    // Also refresh from database to ensure we have the latest data
    this.getCrewMembersFromDB(false);
  }
}


  callbackAfterAddCrew(selectedCrew: CREW_HEADER[]): void {
  console.log('[CrewPage] callbackAfterAddCrew called with crew members:', selectedCrew?.length);
  this.ngZone.run(() => {
    // Refresh crew members from DB
    this.getCrewMembersFromDB(false);

    // Dispatch NgRx action instead of events.publish
    this.store.dispatch(triggerPrefillRefresh());
  });
}
  private noCrewSelected(): boolean {
    const selectedCount = this.crewMembers.filter(m => m.isSelected).length;
    return selectedCount >= 1 ? AppConstants.BOOL_FALSE : AppConstants.BOOL_TRUE;
  }

  filterList(searchText?: string | null) {
  const text = (searchText || '').trim().toLowerCase();

  this.displayCrewMembers = this.crewMembers.filter(item =>
    (item.USER_NAME?.toLowerCase().includes(text)) ||
    (item.DESIGNATION?.toLowerCase().includes(text)) ||
    (item.USER_ID?.toLowerCase().includes(text)) ||
    (item.GLOBALID?.toLowerCase().includes(text)) ||
    (item.PERSON_NO?.toLowerCase().includes(text)) ||
    (item.SOURCE?.toLowerCase().includes(text))
  );

  // keep same re-indexing logic for test automation
  for (let i = 0; i < this.displayCrewMembers.length; i++) {
    this.displayCrewMembers[i].index = 'crew-' + (i + 1);

    for (let k = 0; k < this.selPersonNo.length; k++) {
      const memberId = this.displayCrewMembers[i].SOURCE === 'LOCAL' ?
        this.displayCrewMembers[i].USER_NAME : this.displayCrewMembers[i].PERSON_NO;

      if (memberId === this.selPersonNo[k]) {
        this.displayCrewMembers[i].isSelected = AppConstants.BOOL_TRUE;
        break;
      } else {
        this.displayCrewMembers[i].isSelected = AppConstants.BOOL_FALSE;
      }
    }
  }

  // Check test automation mode
  this.utilityService.isAppRunningInTestAutomationMode().then(isTestMode => {
    if (isTestMode) {
      setTimeout(() => {
        this.updateCheckboxElementsWithIds();
      }, 100);
    }
  });
}


  private updateCheckboxElementsWithIds() {
    this.displayCrewMembers.forEach((_, index) => {
      const element = document.getElementById('crew-' + (index + 1));
      if (element && element.childNodes[1]) {
        (element.childNodes[1] as HTMLElement).id = 'crew-selection-element-' + (index + 1);
      }
    });
  }

  getUserId(crew: CREW_HEADER) {
    return crew.USER_ID;
  }

  getGlobalId(crew: CREW_HEADER) {
    return crew.SOURCE !== 'LOCAL' ? crew.GLOBALID : undefined;
  }

  getSource(crew: CREW_HEADER) {
    return crew.SOURCE !== 'LOCAL' ? crew.PERSON_NO : 'LOCAL';
  }

  hasAnySelection(): boolean {
    return this.selPersonNo.length > 0 || this.displayCrewMembers.some(crew => crew.isSelected);
  }

  closeModal() {
    this.modalCtrl.dismiss();
  }
}
