import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NotifResult } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { filter, Observable, switchMap, take, tap } from 'rxjs';
import { Store } from '@ngrx/store';
import { selectAllNotifications, selectAllTemplates, selectRigData, selectRigLoadedFromDb } from 'src/app/store/store.selector';
import { TEMPLATE_HEADER } from 'src/models/TEMPLATE_HEADER';
import * as RigActions from 'src/app/store/store.actions';
import { IonButtons, IonContent, IonHeader, IonMenuButton, IonRouterOutlet, IonTitle, IonToolbar, MenuController } from '@ionic/angular/standalone';
@Component({
  selector: 'app-forms',
  templateUrl: './forms.page.html',
  styleUrls: ['./forms.page.scss'],
  standalone: true,
  imports: [ CommonModule, FormsModule ,IonToolbar , IonHeader, IonButtons ,IonMenuButton ,IonContent ,IonTitle]
})
export class FormsPage implements OnInit {

  notifications$: Observable<NotifResult[]> | undefined;
  templates$: Observable<TEMPLATE_HEADER[]|null> | undefined;
   isTemplateLoadTriggered = false;
  constructor(private store: Store ,   private routerOutlet: IonRouterOutlet , private menuCtrl: MenuController) { 
      this.routerOutlet.swipeGesture = false;
      this.menuCtrl.swipeGesture(true)
   }

  ngOnInit() {
    this.store.subscribe(state => console.log('AppState:', state));

  

     this.store.dispatch(RigActions.loadRigFromDb());
      
        // When rig data is loaded, check if templates need to be loaded
        this.store.select(selectRigLoadedFromDb).pipe(
          tap((loaded: any) => console.log('Rig data loaded from DB:', loaded)),
          filter((loaded): loaded is boolean => !!loaded),
          switchMap(() => this.store.select(selectRigData).pipe(take(1))), // Get the rig data
          take(1)
        ).subscribe((rigData) => {
          if (!rigData || !rigData.RIG_NO) {
            console.warn('RigData is null or RIG_NO missing — opening site popup');
            // await this.openSiteNumberPopup();
          } else {
            // Dispatch action to load templates only if they're not already loaded
            this.store.dispatch(RigActions.loadAllTemplatesFromDb());
          }
        });
      
        this.store.select(selectAllNotifications).pipe(
          tap(events => {
            console.log('Current notifications:', events);
            // Find the first event of type 4
            const firstEventType4 = events.find(event => event.type === 4);
            
            if (firstEventType4 && !this.isTemplateLoadTriggered) {
              // Only dispatch the action if we haven't already triggered the DB call
              console.log('First event with type 4 found:', firstEventType4);
              this.store.dispatch(RigActions.loadAllTemplatesFromDb());
             
             
             
                 this.templates$ = this.store.select(selectAllTemplates).pipe(
                   tap(loaded => {
                    
                     console.log('Templates loaded from DB:', loaded);
                    //  this.isLoading = !loaded;  // Set loading state based on the `loaded` flag
                   })
                 )
                 this.isTemplateLoadTriggered = true;
            }
          })
        ).subscribe();
      
    // this.notifications$ = this.store.select(selectAllNotifications).pipe(distinctUntilChanged());
  }




  }
