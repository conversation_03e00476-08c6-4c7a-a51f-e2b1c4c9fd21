import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonButtons, IonContent, IonHeader, IonMenuButton, IonRouterOutlet, IonTitle, IonToolbar, MenuController } from '@ionic/angular/standalone';


@Component({
  selector: 'app-inbox',
  templateUrl: './inbox.page.html',
  styleUrls: ['./inbox.page.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule , IonToolbar, IonContent, IonButtons, IonTitle ,IonHeader, IonMenuButton]
})
export class InboxPage implements OnInit {

  constructor( private routerOutlet: IonRouterOutlet,
      private menuCtrl: MenuController) { 
       this.routerOutlet.swipeGesture = false;
      this.menuCtrl.swipeGesture(true)
  }

  ngOnInit() {
  }

}
