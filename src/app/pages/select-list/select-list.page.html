<ion-header>
  <ion-toolbar>
    <ion-title>{{ listDetails.title || ('Select List' | translate) }}</ion-title>

    <ion-buttons slot="start">
      <ion-button
        (click)="close()"
        *ngIf="listDetails.multiSelect && !listDetails.hideCloseBtn">
        {{ 'Close' | translate }}
      </ion-button>
    </ion-buttons>

    <ion-buttons slot="end">
      <ion-button
        (click)="saveSelection()"
        *ngIf="listDetails.multiSelect">
        {{ 'Save' | translate }}
      </ion-button>
      <ion-button
        (click)="saveSelection(true)"
        *ngIf="!listDetails.multiSelect">
        {{ 'Done' | translate }}
      </ion-button>
    </ion-buttons>
  </ion-toolbar>

  <ion-grid class="bg-g">
    <ion-searchbar
      #searchBar
      class="search-toolbar"
      [placeholder]="listDetails.searchPlaceHolder || ('Search List' | translate)"
      [(ngModel)]="listDetails.searchValue"
      (ionInput)="filterList('DisplayString', listDetails.searchValue || '')"
      (ionCancel)="filterList('DisplayString', listDetails.searchValue || '')">
    </ion-searchbar>
  </ion-grid>
</ion-header>

<ion-content [class.contrast]="theme === 'contrast'">
  <!-- Multi-select list -->
  <ion-list *ngIf="listDetails.multiSelect">
    <ion-item
      button
      detail="false"
      *ngFor="let data of displayListData; let i = index"
      [attr.id]="data['data_id']"
      (click)="toggleSelectData(data)"
      [class.has-error]="hasError">

      <ion-checkbox
        slot="start"
        class="checkbox-custom"
        [attr.id]="'data-selection-element-' + (i + 1)"
        [(ngModel)]="data[selectKey]">
      </ion-checkbox>

      <ion-label>{{ data.DisplayString || "-" }}</ion-label>
    </ion-item>
  </ion-list>

  <!-- Single-select list (radios) -->
  <ion-radio-group
    *ngIf="!listDetails.multiSelect"
    [value]="radioItemSelected"
    (ionChange)="onRadioGroupChange($event)">

    <ion-item
      button
      detail="false"
      *ngFor="let data of displayListData; let i = index"
      [attr.id]="data['data_id']"
      [class.has-error]="hasError">

      <ion-radio
        slot="start"
        class="radio-custom"
        [attr.id]="'data-selection-element-' + (i + 1)"
        [value]="data.DisplayString">
      </ion-radio>

      <ion-label>{{ data.DisplayString || "-" }}</ion-label>
    </ion-item>
  </ion-radio-group>
</ion-content>
