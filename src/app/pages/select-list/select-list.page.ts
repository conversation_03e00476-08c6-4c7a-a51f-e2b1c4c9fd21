import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild, ViewChildren, QueryList } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  IonButton,
  IonButtons,
  IonCheckbox,
  IonContent,
  IonHeader,
  IonIcon,
  IonItem,
  IonLabel,
  IonList,
  IonRadio,
  IonRadioGroup,
  IonSearchbar,
  IonTitle,
  IonToolbar,
  ModalController,
  IonGrid
} from '@ionic/angular/standalone';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

export interface SelectListItem {
  DisplayString: string;
  object?: any;
  isSelected?: boolean;
  [key: string]: any; // to allow data_id, dynamic selection key, etc.
}

export interface SelectListDetails {
  title?: string;
  searchPlaceHolder?: string;
  selectKey?: string;         // defaults to "isSelected"
  multiSelect?: boolean;      // defaults to false
  eventName?: string;         // ignored in this Ionic 8 version
  hideCloseBtn?: boolean;     // defaults to false
  searchValue?: string;       // defaults to ""
}

@Component({
  selector: 'app-select-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    // Ionic standalone elements
    IonHeader, IonToolbar, IonTitle, IonButtons, IonButton, IonIcon,
    IonContent, IonSearchbar, IonList, IonItem, IonLabel, IonRadioGroup, IonRadio, IonCheckbox, IonGrid
  ],
  templateUrl: './select-list.page.html',
  styleUrls: ['./select-list.page.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SelectListPage implements OnInit {
  /** Inputs */
  @Input() listData: SelectListItem[] = [];
  @Input() listDetails: SelectListDetails = {};
  @Input() theme: 'normal' | string = 'normal';

  /** Outputs (if embedded as a component, not a modal) */
  @Output() dismissed = new EventEmitter<any>();

  /** Template refs */
  @ViewChild('searchBar', { static: false }) searchBar?: IonSearchbar;
  @ViewChildren(IonItem, { read: ElementRef }) itemEls!: QueryList<ElementRef>;

  /** Local state */
  displayListData: SelectListItem[] = [];
  radioItemSelected?: string;
  hasError = true;

  /** Public getter so template can access it */
  get selectKey(): string {
    return this.listDetails.selectKey || 'isSelected';
  }

  constructor(private modalCtrl: ModalController) {}

  ngOnInit(): void {
    // Defaults (same semantics as original)
    this.listDetails = {
      title: this.listDetails?.title ?? 'Select List',
      searchPlaceHolder: this.listDetails?.searchPlaceHolder ?? 'Search List',
      selectKey: this.listDetails?.selectKey || 'isSelected',
      multiSelect: this.listDetails?.multiSelect ?? false,
      hideCloseBtn: this.listDetails?.hideCloseBtn ?? false,
      searchValue: this.listDetails?.searchValue ?? '',
      eventName: this.listDetails?.eventName, // ignored
    };

    // Normalize and copy into display list
    this.listData = (this.listData || []).map((item, idx) => {
      const selected = item[this.selectKey] === true;
      if (selected && !this.listDetails.multiSelect) {
        this.radioItemSelected = item.DisplayString;
      }
      // stable ids for automation
      return { ...item, data_id: `data-${idx + 1}`, [this.selectKey]: !!selected };
    });

    this.displayListData = [...this.listData];
    this.hasError = this.noDataSelected();
  }

  // Filter by key (kept same signature)
  filterList(key: string, searchText: string): void {
    if (!key) return;
    const term = (searchText || '').trim().toLowerCase();
    if (!term) {
      this.displayListData = [...this.listData];
      return;
    }
    this.displayListData = this.listData.filter(
      (item) => (item[key] || '').toString().toLowerCase().includes(term)
    );
  }

  // Checkbox (multi-select) toggle — preserves original behavior
  toggleSelectData(item: SelectListItem): void {
    if (!this.listDetails.multiSelect) return;
    item[this.selectKey] = !item[this.selectKey];
    // refresh arrays similar to original to ensure change detection and parity
    this.listData = JSON.parse(JSON.stringify(this.displayListData));
    this.hasError = this.noDataSelected();
  }

  // Radio group change — immediately publish & close (same as original toggleRadioData)
  onRadioGroupChange(ev: CustomEvent): void {
    if (this.listDetails.multiSelect) return;
    const value = (ev.detail as any)?.value as string | undefined;
    this.radioItemSelected = value;
    const selectedItem = this.displayListData.find(d => d.DisplayString === value);
    if (selectedItem) {
      // mirror original: refresh list, then publish
      this.displayListData = JSON.parse(JSON.stringify(this.displayListData));
      this.publishAndClose(selectedItem);
    }
  }

  // Save button (returns all when multi-select; or creates new entry if requested)
  saveSelection(newEntry?: boolean): void {
    let data: any;
    if (this.listDetails.multiSelect) {
      data = this.listData;
    }

    if (newEntry) {
      data = { newEntry: true, value: '' };
      const val = this.listDetails.searchValue || '';
      data.value = val.length > 50 ? val.substring(0, 50) : val;
    }

    this.publishAndClose(data);
  }

  // Close without data (same as old "Close" when multiSelect)
  close(): void {
    if (this.modalCtrl) {
      this.modalCtrl.dismiss(null, 'cancel');
    }
    this.dismissed.emit(null);
  }

  /** Helpers */
  private publishAndClose(data: any): void {
    if (this.modalCtrl) {
      this.modalCtrl.dismiss(data ?? null, 'ok');
    }
    this.dismissed.emit(data ?? null);
  }

  private noDataSelected(): boolean {
    const key = this.selectKey;
    for (let i = 0; i < this.listData.length; i++) {
      if (this.listData[i][key]) return false;
    }
    return true;
  }
}
