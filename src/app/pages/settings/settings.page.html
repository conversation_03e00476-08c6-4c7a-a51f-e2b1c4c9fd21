<ion-header>
  <ion-toolbar color="primary" class="custom-toolbar">
    <ion-buttons slot="start">
      <ion-menu-button autoHide="false" class="white-icon"></ion-menu-button>
    </ion-buttons>
    <ion-title class="left-title">Settings</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-list>
    <!-- Language Selector -->
    <ion-item>
      <ion-label>Language</ion-label>
      <ion-select
        slot="end"
        interface="popover"
        [value]="utilityService.lang$ | async"
        (ionChange)="utilityService.changeLanguage($event.detail.value)"
        placeholder="Select Language"
      >
        <ion-select-option value="en">English</ion-select-option>
        <ion-select-option value="es">Español</ion-select-option>
        <ion-select-option value="ar">العربية</ion-select-option>
      </ion-select>
    </ion-item>
  </ion-list>

  <ion-accordion-group>
  <ion-accordion value="first">
   
    <ion-item slot="header" class="accordion-header-row">
        <span class="icon-and-label">
    <i class="fa-solid fa-rotate-right header-icon" style="color: black;" (click)="onRigDocSync(true)" ></i>
      <ion-label class="header-label">{{'Start HSE Document Sync' | translate}}</ion-label>
      </span>
    </ion-item>
         
    <ion-item class="crsr" slot="content">
  <div style="width:100%;" class="ion-padding sync-content-align">
    <p class="accordion-description">
      Clicking on this button starts the process of syncing documents such as CTAs from the backend to your device.<br>
      <b>Note: This process requires network connectivity.</b>
    </p>
    <div *ngIf="isSyncing">
      <span>
        Syncing
        <b class="sync-file-name">{{ currentSyncFile }}</b>
        ({{ (progress * 100) | number:'1.0-0' }}%)
      </span>
    </div>
    <br/>
    <div
     *ngIf="isSyncing && progress >= 0 && progress < 1" 
    >
      <ion-progress-bar
        [value]="progress"
        
      ></ion-progress-bar>
    </div>
  </div>
</ion-item>

  </ion-accordion>
  <ion-accordion value="second">
    <ion-item slot="header">
      <ion-label  >{{'Get All' | translate}}</ion-label>
    </ion-item>
 <div class="ion-padding" slot="content">
            <ion-item class="crsr" lines="none">
            <ion-avatar item-start>
                <ion-icon name="refresh-outline" (click)="initialData()"></ion-icon>
            </ion-avatar>
  
            <p style="margin-top: 0px; margin-bottom: 0px;">This button downloads the following information:</p>
              </ion-item>
              <ion-item lines="none">
    <ul style="margin-top: 0px; margin-bottom: 0px;">
      <li>HSE Documents and CTA Information</li>
      <li>Site Managers – From SAP HR data</li>
      <li>Crew List – From SAP Tour Sheet information</li>
      <li>Synchronize Form Changes</li>
      <li>Template Changes</li>
      <li>Operator Information</li>
    </ul>
              </ion-item>

              <ion-item lines="none">
   <p class="note" >
      <strong>Note:</strong> This process will not delete temporary crew members and requires network connectivity.
    </p>
      
              </ion-item>

 
    </div> 
  </ion-accordion>
  <ion-accordion value="third">
    <ion-item slot="header"  >
      <ion-label>{{"Switch Site" | translate}}</ion-label>
    </ion-item>
    <div class="ion-padding" slot="content">
    <ion-item class="crsr" (click)="isRigPresent()" lines="none">
            <ion-avatar item-start>
               <ion-icon name="refresh-outline"></ion-icon>
            </ion-avatar>
            <div>
  <p style="margin-top: 0px; margin-bottom: 0px;"> This allows the user to switch to a new site and will:</p>   
            </div>
    </ion-item>
            <ion-item lines="none">
  <ul style="margin-top: 0px; margin-bottom: 0px;">
 <li>Download forms for the new site</li>
                <li>Show templates specific to the new site</li>
                </ul>
            </ion-item>

       <ion-item lines="none">
   <p class="note">
      <strong>Note:</strong> Submit in-progress forms prior to switching sites. Requires network connectivity.
    </p>
       </ion-item>
               
   
 
     
     

</div>

  </ion-accordion>

  <ion-accordion value="fourth">
      <ion-item slot="header" >
      <ion-label>{{'Clear Data' | translate}}</ion-label>
    </ion-item>
<div class="ion-padding" slot="content">
    <ion-item class="crsr" (click)="logout()">
            <ion-avatar item-start>
              <ion-icon name="log-out-outline"></ion-icon>
            </ion-avatar>

            <ul>
                <li>Deletes all data from the Forms app including application logs (required for troubleshooting)</li>
                <li>Requires a close and reopening of the Forms app </li> 
                <li>After login, the following are downloaded:
                    <ul>
                        <li>All Active Templates</li>
                        <li>HSE Documents and CTA Information</li>
                        <li>Site Managers</li>
                        <li>Crew List</li>
                        <li>Operator Information</li>
                        <li>Current forms for the selected site</li>
                    </ul>
                </li>
            </ul>
        </ion-item>
</div>
  </ion-accordion>
</ion-accordion-group>
</ion-content>
