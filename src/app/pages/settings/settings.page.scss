.sync-file-name {
  display: inline-block;
  max-width: 14em; /* Adjust to fit about 9-10 words */
  vertical-align: bottom;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sync-content-align {
  margin-left: 25px;           /* Aligns to text start */
  padding-top: 4px;
  padding-bottom: 8px;
  width: 100%;
}
.accordion-description {
  margin: 4px 0 8px 0;
  font-size: 1rem;
  line-height: 1.32;
}

.accordion-header-row .icon-and-label {
  display: flex;
  align-items: center;
}
.header-icon {
  margin-right: 14px;   /* Space between icon and text */
  font-size: 1.2em;
}

.compact-list {
  margin: 0;
  padding-left: 16px; /* Keep bullet indent */
}

.compact-list li {
  margin: 2px 0;
}
