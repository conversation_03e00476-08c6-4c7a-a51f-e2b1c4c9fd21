import { Compo<PERSON>, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>, On<PERSON><PERSON><PERSON>,CUSTOM_ELEMENTS_SCHEMA, } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AlertController, Platform } from '@ionic/angular';
import { UtilityService } from 'src/app/services/utility.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { AppConstants } from 'src/app/constants/appConstants';
import { AttachmentHelper } from 'src/app/helpers/attachment-helper';
import { HttpClient } from '@angular/common/http';
import { addIcons } from 'ionicons';
import { refreshOutline } from 'ionicons/icons';
import { LoginListenerType, LoginParameters, LoginResult, LoginType, RequestType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { DataService } from 'src/app/services/data.service';
import { SP_SITE_HEADER } from 'src/models/SP_SITE_HEADER';
import { SynclogicService } from 'src/app/services/synclogic.service';
import { IonAccordionGroup, IonAvatar, IonButtons, IonContent, IonHeader, IonIcon, IonItem, IonLabel, IonList, IonMenuButton, IonProgressBar, IonSelect, IonSelectOption, IonTitle, IonToolbar, IonAccordion, IonRouterOutlet, MenuController, PopoverController } from '@ionic/angular/standalone';
import { SiteRigModalComponent } from 'src/app/components/site-rig-modal/site-rig-modal.component';
import { combineLatest, Observable, take } from 'rxjs';
import { RIG_HEADER } from 'src/models/RIG_HEADER';
import { selectProgressPercentage, selectRigLoadedFromDb } from 'src/app/store/store.selector';
import { Store } from '@ngrx/store';

addIcons({ 'refresh-outline': refreshOutline });


@Component({
  selector: 'app-settings',
  templateUrl: './settings.page.html',
  styleUrls: ['./settings.page.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, TranslateModule, IonAccordionGroup, IonItem, IonMenuButton, IonToolbar, IonAvatar, IonLabel, IonProgressBar, IonContent, IonButtons, IonList, IonHeader, IonTitle, IonSelect, IonSelectOption, IonIcon, IonAccordion],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SettingsPage implements OnInit, OnDestroy {
  isSyncing = false;
  progress = 0;
  currentSyncFile = '';
progress$!: Observable<number | null>;
rigData$!: Observable<RIG_HEADER | null>
  currentGraphApiToken: string | null = null;
  networkError = false;
  private syncInterval: any = null;
  private SYNC_PERIOD_MS = 1000 * 60 * 10; 
  
  constructor(
    public utilityService: UtilityService,
    public router: Router,
    public alertController: AlertController,
    public translate: TranslateService,
    private http: HttpClient,
    private attachmentHelper: AttachmentHelper,
    private unviredSDK: UnviredCordovaSDK,
    private dataService: DataService,
    private synclogicService: SynclogicService,
    private platform: Platform,
    private ngZone: NgZone,
    private routerOutlet: IonRouterOutlet,
    private menuCtrl: MenuController,
    private store: Store,
    private popoverController: PopoverController,
  ) { 
       this.routerOutlet.swipeGesture = false;
      this.menuCtrl.swipeGesture(true)
  }

   ngOnInit() {
    this.synclogicService.isSyncing$.subscribe(val => this.isSyncing = val);
    this.synclogicService.progress$.subscribe(val => this.progress = val);
    this.synclogicService.currentSyncFile$.subscribe(val => this.currentSyncFile = val);
    
    this.startPeriodicSync();
    
    this.platform.pause.subscribe(() => {
      this.ngZone.run(() => this.onRigDocSync(false));
    });
    this.platform.resume.subscribe(() => {
      this.ngZone.run(() => this.onRigDocSync(false));
    });

      this.progress$ = this.store.select(selectProgressPercentage);

  }

  ngOnDestroy() {
    if (this.syncInterval) clearInterval(this.syncInterval);
  }

  startPeriodicSync() {
    if (this.syncInterval) clearInterval(this.syncInterval);
    this.syncInterval = setInterval(() => {
      this.ngZone.run(() => this.onRigDocSync());
    }, this.SYNC_PERIOD_MS);
  }

  async handleInitialDataDownloaded() {
    try {
      console.log('Initial data has been downloaded!');
      await this.onRigDocSync();
    } catch (err) {
      console.error('Error in handleInitialDataDownloaded:', err);
    }
  }


  async logout() {
    const alert = await this.alertController.create({
      header: this.translate.instant("Clear Data"),
      message: `${AppConstants.CLEAR_DATA_CONFIRM_MSG}`,
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => { },
        },
        {
          text: this.translate.instant('Clear Data'),
          role: 'confirm',
          cssClass: 'secondary',
          handler: async () => {
            let logoutResult: any;
            try {
              logoutResult = await this.unviredSDK.clearData();
              console.log(logoutResult.data);
            } catch (error: any) {
              console.log('Error while logging out: ' + error.message);
            } finally {
              this.router.navigate(['/login']);
              console.log(`logout to initial page********** ${JSON.stringify(logoutResult)}`);
            }
          }
        }
      ]
    });
    await alert.present();
  }


  onRigDocSync(triggeredByUser = true) {
    this.synclogicService.onRigDocSync(triggeredByUser);
  }

  // Authentication/Token helpers

  async initializeLoginProcess() {
    try {
      let loginParams = new LoginParameters();
      loginParams.url = `https://umpdev.pd.com:8443`;
      loginParams.company = 'PD';
      loginParams.jwtOptions = { app: 'PD_FORMS', language: 'en' };
      loginParams.appName = AppConstants.APPLICATION_NAME;
      loginParams.metadataPath = 'assets/metadata.json';
      loginParams.cacheWebData = true;
      let loginResult = await this.unviredSDK.login(loginParams);

      switch (loginResult.type) {
        case LoginListenerType.auth_activation_required:
            
              this.router.navigateByUrl('/login', { replaceUrl: true });
          
          // Display Login Screen |auth_activation_required|
          // if (this.platform === 'browser') {
          //   this.ngZone.run(() => {
          //     this.route.navigateByUrl('/login', { replaceUrl: true });
          //   });
          // } else {
          //   this.ngZone.run(() => {
          //     this.route.navigateByUrl('/mobile-login', { replaceUrl: true });
          //   });
          // }
          break;
        case LoginListenerType.app_requires_login:
          this.router.navigateByUrl('/login', { replaceUrl: true });
 
          // Display Login Screen |app_requires_login|
          // if (this.platform === 'browser') {
          //   this.ngZone.run(() => {
          //     this.route.navigateByUrl('/login'), { replaceUrl: true };
          //   });
          // } else {
          //   this.ngZone.run(() => {
          //     this.route.navigateByUrl('/mobile-login', { replaceUrl: true });
          //   });
          // }
          break;
        case LoginListenerType.login_success:
          // Srinidhi: 4 Oct 2022: Set this flag so that when the users navigate to the home page, then it starts downloading customization data.
          // this.isStartCustomization = true;
          console.log('sucess')
          // this.displayLandingPage();
          break;
      }
    } catch (error) {
      this.unviredSDK.logError('data service', 'initializeLoginProcces()', 'ERROR: ' + error);
    }
  }


  async getCustomization() {
    this.onRigDocSync(true);
  }


isRigPresent() {
  console.log('check is rig present');

  if (!this.rigData$) {
    // rigData$ is not set yet — show alert instead of error
    this.alertController.create({
      header: 'Please Wait',
      message: 'Rig data downloading...',
      buttons: ['OK']
    }).then(alert => alert.present());
    return;
  }

  combineLatest([
    this.rigData$.pipe(take(1)),
    this.progress$.pipe(take(1))
  ])
  .subscribe(([rig, progress]) => {
    if (rig?.RIG_NO && progress !== null && Math.round(progress) === 100) {
      this.openSiteNumberPopup();
    } else {
      this.alertController.create({
        header: 'Please Wait',
        message: 'Rig data downloading...',
        buttons: ['OK']
      }).then(alert => alert.present());
    }
  });
}




  async openSiteNumberPopup() {
    console.log('openSiteNumberPopup called from side menu');
    const modal = await this.popoverController.create({
      component: SiteRigModalComponent,
      cssClass: 'custom-site-modal',
      backdropDismiss: true,
      mode: 'md'
    });
  
    modal.onDidDismiss().then((result: any) => {
      if (result) {
        console.log('Site number submitted:', result);
        // this.saveSiteNumber(result.data);
      }
    });
  
    await modal.present();
  }

  async initialData(){
     await this.dataService.initialDataDownloadCall()
     await this.getCustomization()
  }
  }
