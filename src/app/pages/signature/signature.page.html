<ion-header>
  <ion-toolbar>
    <ion-title>{{ 'Signature' | translate }}</ion-title>
    <ion-buttons slot="start">
      <ion-button (click)="dismiss()">{{ 'Close' | translate }}</ion-button>
    </ion-buttons>
    <ion-buttons slot="end">
      <ion-button (click)="clearSign()">{{ 'Clear' | translate }}</ion-button>
      <ion-button (click)="saveSign()" [disabled]="isSignaturePadEmpty()">{{ 'Save' | translate }}</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [ngClass]="{'contrast': styleTheme === 'contrast'}" class="ion-padding">
  <ion-list>
    <ion-item>
      <h2>{{ crewDet.CREW_NAME }}</h2>
      <p *ngIf="crewDet.CREW_POS">{{ crewDet.CREW_POS }}</p>
    </ion-item>
  </ion-list>

  <div class="sign-container">
    <canvas #canvas width="575" height="420"></canvas>
  </div>

  <ion-list lines="none">
    <ion-item *ngIf="isSignDrawn">
      <p>Note: Please clear and redraw the signature to overwrite it.</p>
    </ion-item>
  </ion-list>
</ion-content>
