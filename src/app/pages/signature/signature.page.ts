import { Component, ViewChild, ElementRef, NgZone, AfterViewInit } from '@angular/core';
import { ModalController, NavParams } from '@ionic/angular/standalone';
import SignaturePad from 'signature_pad';
import { IonButtons, IonButton, IonContent, IonHeader, IonTitle, IonToolbar, IonList, IonItem } from '@ionic/angular/standalone';
import { AppConstants } from '../../constants/appConstants';
import { UtilityService } from '../../services/utility.service';
import { TranslateService, TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-signature',
  templateUrl: './signature.page.html',
  styleUrls: ['./signature.page.scss'],
  imports: [IonButtons, IonButton, IonContent, IonHeader, IonTitle, IonToolbar, IonList, IonItem, TranslateModule, CommonModule],
  standalone: true,
})
export class SignaturePage implements AfterViewInit {

  @ViewChild('canvas', { static: true }) canvasRef!: ElementRef<HTMLCanvasElement>;
  signaturePad!: SignaturePad;

  signatureType: string = '';
  crewDet: any = {};
  isSignDrawn: boolean = false;
  styleTheme: string = 'normal';

  signaturePadOptions = {
    minWidth: 2,
    penColor: 'rgb(0, 81, 255)',
  };

  constructor(
    private modalCtrl: ModalController,
    private navParams: NavParams,
    private ngZone: NgZone,
    private utilityService: UtilityService,
    private translate: TranslateService
  ) {
    try {
      console.log('SignaturePage - Constructor started');
      console.log('SignaturePage - NavParams:', this.navParams);

      this.signatureType = this.navParams.get('signatureType');
      this.crewDet = this.navParams.get('crew') || {};
      this.styleTheme = this.navParams.get('theme') || 'normal';

      console.log('SignaturePage - Constructor data:');
      console.log('  - signatureType:', this.signatureType);
      console.log('  - crewDet:', this.crewDet);
      console.log('  - styleTheme:', this.styleTheme);

      // Add global error handler for this component
      window.addEventListener('error', (event) => {
        console.error('SignaturePage - Global Error:', event.error);
        console.error('SignaturePage - Error Stack:', event.error?.stack);
      });

      window.addEventListener('unhandledrejection', (event) => {
        console.error('SignaturePage - Unhandled Promise Rejection:', event.reason);
      });

    } catch (error) {
      console.error('SignaturePage - Constructor Error:', error);
    }
  }

  ngAfterViewInit() {
    try {
      console.log('SignaturePage - ngAfterViewInit - crewDet:', this.crewDet);
      console.log('SignaturePage - Canvas element:', this.canvasRef?.nativeElement);

      if (!this.canvasRef?.nativeElement) {
        console.error('SignaturePage - Canvas element not found!');
        return;
      }

      console.log('SignaturePage - Creating SignaturePad with options:', this.signaturePadOptions);
      this.signaturePad = new SignaturePad(this.canvasRef.nativeElement, this.signaturePadOptions);
      console.log('SignaturePage - SignaturePad initialized successfully');
      console.log('SignaturePage - SignaturePad instance:', this.signaturePad);

      // Add event listeners to track signature drawing
      this.signaturePad.addEventListener('beginStroke', () => {
        console.log('SignaturePage - User started drawing');
      });

      this.signaturePad.addEventListener('endStroke', () => {
        console.log('SignaturePage - User finished drawing stroke');
        this.isSignDrawn = !this.signaturePad.isEmpty();
      });

      this.ngZone.run(async () => {
        try {
          if (this.crewDet && this.crewDet.CREW_SIGN) {
            console.log('SignaturePage - Loading existing signature');
            this.isSignDrawn = true;
            this.signaturePad.fromDataURL(this.crewDet.CREW_SIGN);
            this.signaturePad.off();
          } else {
            const isTestMode = await this.utilityService.isAppRunningInTestAutomationMode();
            if (isTestMode) {
              console.log('SignaturePage - Loading test signature');
              this.isSignDrawn = false;
              this.signaturePad.fromDataURL(AppConstants.SETTING_TEST_SIGNATURE);
              this.signaturePad.off();
            } else {
              console.log('SignaturePage - Initializing empty signature pad');
              this.isSignDrawn = false;
              this.signaturePad.on();
              this.signaturePad.clear();
            }
          }
        } catch (error) {
          console.error('SignaturePage - Error in ngZone.run:', error);
        }
      });
    } catch (error) {
      console.error('SignaturePage - Error in ngAfterViewInit:', error);
    }
  }

  dismiss() {
    this.modalCtrl.dismiss({});
  }

  saveSign() {
    try {
      console.log('SignaturePage - Saving signature');
      const signImg = this.signaturePad.isEmpty() ? '' : this.signaturePad.toDataURL();
      console.log('SignaturePage - Signature data length:', signImg.length);

      this.modalCtrl.dismiss({
        CREW_SIGN: signImg,
        signatureType: this.signatureType,
        crew: this.crewDet
      });
    } catch (error) {
      console.error('SignaturePage - Error saving signature:', error);
    }
  }

  clearSign() {
    try {
      this.ngZone.run(() => {
        console.log('SignaturePage - Clearing signature');
        this.isSignDrawn = false;
        this.signaturePad.on();
        this.signaturePad.clear();
        console.log("SignaturePage - Signature cleared. Is empty?", this.signaturePad.isEmpty());
      });
    } catch (error) {
      console.error('SignaturePage - Error clearing signature:', error);
    }
  }

  // Helper method to check if signature pad is empty (for template binding)
  isSignaturePadEmpty(): boolean {
    return this.signaturePad ? this.signaturePad.isEmpty() : true;
  }

  // Debug method to check component state
  debugSignaturePage() {
    console.log('=== SIGNATURE PAGE DEBUG INFO ===');
    console.log('signatureType:', this.signatureType);
    console.log('crewDet:', this.crewDet);
    console.log('styleTheme:', this.styleTheme);
    console.log('isSignDrawn:', this.isSignDrawn);
    console.log('canvasRef:', this.canvasRef);
    console.log('canvasRef.nativeElement:', this.canvasRef?.nativeElement);
    console.log('signaturePad:', this.signaturePad);
    console.log('signaturePadOptions:', this.signaturePadOptions);

    if (this.canvasRef?.nativeElement) {
      const canvas = this.canvasRef.nativeElement;
      console.log('Canvas width:', canvas.width);
      console.log('Canvas height:', canvas.height);
      console.log('Canvas clientWidth:', canvas.clientWidth);
      console.log('Canvas clientHeight:', canvas.clientHeight);
    }

    if (this.signaturePad) {
      console.log('SignaturePad isEmpty:', this.signaturePad.isEmpty());
      try {
        console.log('SignaturePad toDataURL length:', this.signaturePad.toDataURL().length);
      } catch (e) {
        console.log('SignaturePad toDataURL error:', e);
      }
    }
    console.log('=== END DEBUG INFO ===');
  }
}
