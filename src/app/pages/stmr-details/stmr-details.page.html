<ion-header>
  <ion-toolbar class="main-toolbar" color="primary">
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="close()">
        <ion-icon name="arrow-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>{{ 'STMR Details' | translate }}</ion-title>

    <ion-buttons slot="end">
      <ion-button fill="primary" (click)="presentSettingsPopover($event)">
        <ion-icon name="cog-outline" style="color: white;"></ion-icon>
      </ion-button>

      <ion-button fill="solid"
                  color="light"
                  class="header-btn"
                  (click)="localSaveSTMR()"
                  *ngIf="!isSTMRReadonly(stmrHeader)">
        {{ 'Save' | translate }}
      </ion-button>

      <ion-button fill="solid"
                  color="light"
                  class="header-btn"
                  (click)="saveAndExitSTMR()"
                  *ngIf="!isSTMRReadonly(stmrHeader)">
        {{ 'Save And Exit' | translate }}
      </ion-button>

      <ion-button fill="solid"
                  color="warning"
                  class="header-btn"
                  (click)="submitSTMR()"
                  [disabled]="!isSTMRComplete"
                  *ngIf="!isSTMRReadonly(stmrHeader)">
        <i class="fa-solid fa-paper-plane"></i>
        {{ 'Send' | translate }}
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>


<ion-content>
  <!-- Details Section -->
  <div class="section-header">
    <h2>{{ 'Details' | translate }}</h2>
  </div>

  <div class="details-content">
<div class="details-grid">
  <!-- Row 1 -->
  <div class="detail-item">
    <span class="label">{{ 'Company #' | translate }}:</span>
    <span class="value">{{ stmrHeader?.COMP_CODE || '-' }}</span>
  </div>
<div class="detail-item">
    <span class="label">{{ 'Site #' | translate }}:</span>
    <span class="value">{{ stmrHeader?.RIG_NO || '' }}</span>
  </div>
  <div style="padding: 20px;">
  </div>

  <!-- Row 2 -->
  <div class="detail-item">
    <span class="label">{{ 'STMR #' | translate }}:</span>
    <span class="value">{{ getSTMRIDForDisplay(stmrHeader?.STMR_ID) || 'New' }}</span>
  </div>
  <div class="detail-item">
    <span class="label">{{ 'Created' | translate }}:</span>
    <span class="value">{{ returnDisplayDate(stmrHeader?.SHIFT_TIME, '', 'MMM DD YYYY HH:mm') || '-' }}</span>
  </div>
  <div class="detail-item">
  <span class="label" [ngClass]="{'error-text': !stmrHeader.SHIFT}">
    {{ 'Shift Type' | translate }}:
  </span>
  <div class="radio-group">
    <label class="radio-option">
      <input type="radio"
             [disabled]="isSTMRReadonly(stmrHeader)"
             color="primary"
             name="shiftType"
             value="NIGHT"
             [(ngModel)]="stmrHeader.SHIFT"
             (ngModelChange)="onModelChange($event)">
      <span class="radio-label">{{ 'Night Shift' | translate }}</span>
    </label>
    <label class="radio-option">
      <input type="radio"
             [disabled]="isSTMRReadonly(stmrHeader)"
             color="primary"
             name="shiftType"
             value="DAY"
             [(ngModel)]="stmrHeader.SHIFT"
             (ngModelChange)="onModelChange($event)">
      <span class="radio-label">{{ 'Day Shift' | translate }}</span>
    </label>
  </div>
</div>


  <!-- Row 3 -->
  
   <div class="detail-item">
    <span class="label" [ngClass]="{'error-text': !stmrHeader.WELL_LOC}">{{ 'Location' | translate }}:</span>
    <input type="text" class="detail-input" [ngClass]="{'error-input': !stmrHeader.WELL_LOC}" [(ngModel)]="stmrHeader.WELL_LOC" placeholder="Location">
  </div>
  <div class="detail-item">
    <span class="label" [ngClass]="{'error-text': !stmrHeader.CHAIRED_BY}">{{ 'Chaired By' | translate }}:</span>
    <div class="input-with-dropdown">
      <input type="text" class="detail-input" [ngClass]="{'error-input': !stmrHeader.CHAIRED_BY}" [(ngModel)]="stmrHeader.CHAIRED_BY" placeholder="Name">
      <ion-button fill="clear" class="dropdown-btn" (click)="openSelectCrew('CHAIRED_BY', stmrHeader.CHAIRED_BY, stmrHeader.CHAIRED_BY)">
        <ion-icon name="chevron-down-outline"></ion-icon>
      </ion-button>
    </div>
  </div>
  <div class="detail-item">
    <span class="label">{{ 'Operator' | translate }}:</span>
    <span class="value">{{ stmrHeader?.OPERATOR.NAME || '' }}</span>
  </div>
</div>
  </div>

  <!-- Topics Section -->
  <div class="section-header">
    <h2>{{ 'Topics' | translate }}</h2>
    <ion-button fill="clear"
                class="add-btn"
                *ngIf="!isSTMRReadonly(stmrHeader)"
                (click)="addTopic()">
      <ion-icon name="add-circle-outline"></ion-icon>
    </ion-button>
  </div>

  <div class="section-content">
    <div class="empty-message"
         *ngIf="stmrTopicEntity.length === 0 || getNoOfDeletedTopics(stmrTopicEntity) === stmrTopicEntity.length"
         (click)="addTopic()">
      <span class="error-text">{{ 'No Topics added.' | translate }}</span>
    </div>

    <div class="topics-list" *ngIf="stmrTopicEntity.length > 0">
      <div class="topic-item"
           *ngFor="let entity of stmrTopicEntity; let i = index"
           [style.display]="entity.topic.P_MODE === 'D' ? 'none' : 'block'"
           (click)="editTopic(entity, i)">
        <div class="topic-info">
          <h3>{{ entity.topic.TOPIC_NAME }}</h3>
          <span class="topic-time">{{ dispTime(entity.topic.TOPIC_START) }}</span>
          <p>{{ entity.topic.TOPIC_NOTE }}</p>
        </div>
        <ion-button fill="clear"
                    color="danger"
                    (click)="removeTopic($event, i)"
                    [disabled]="isSTMRReadonly(stmrHeader)">
          <ion-icon name="trash-outline"></ion-icon>
        </ion-button>
      </div>
    </div>
  </div>

  <!-- Crew Section -->
  <div class="section-header">
    <h2>{{ 'Crew' | translate }}</h2>
    <ion-button fill="clear"
                class="add-btn"
                *ngIf="!isSTMRReadonly(stmrHeader)"
                (click)="addCrew()">
      <ion-icon name="person-add-outline"></ion-icon>
    </ion-button>
  </div>

  <div class="section-content">
    <div class="empty-message"
         *ngIf="getNoOfNonThirdPartyCrewMembers(stmrCrew) === 0"
         (click)="addCrew()">
      <span class="error-text">{{ 'No Crew members added.' | translate }}</span>
    </div>

    <div class="crew-list" *ngIf="getNoOfNonThirdPartyCrewMembers(stmrCrew) > 0">
      <div class="crew-item"
           *ngFor="let crew of stmrCrew"
           [style.display]="crew.CREW_TYPE === 'THIRD_PARTY' || crew.P_MODE === 'D' ? 'none' : 'block'">
        <div class="crew-info">
          <h3>{{ crew.CREW_NAME }}</h3>
          <p>{{ crew.CREW_POS }}</p>
        </div>
        <div class="crew-actions">
          <ion-button *ngIf="!crew.CREW_SIGN"
                      fill="outline"
                      color="danger"
                      class="signature-btn"
                      (click)="captureSign('CREW_SIGN', crew)">
            {{ 'Tap to Add Signature' | translate }}
          </ion-button>
          <div *ngIf="crew.CREW_SIGN" class="signature-display" (click)="captureSign('CREW_SIGN', crew)">
            <img [src]="crew.CREW_SIGN" alt="Crew Signature" class="signature-image" />
            <p class="signature-label">{{ 'Tap to Edit Signature' | translate }}</p>
          </div>
          <ion-button fill="clear"
                      color="medium"
                      class="remove-btn"
                      (click)="removeCrewMember(crew)"
                      *ngIf="!isSTMRReadonly(stmrHeader)">
            <ion-icon name="close-outline"></ion-icon>
          </ion-button>
        </div>
      </div>
    </div>
  </div>

  <!-- 3rd Party Section -->
  <div class="section-header">
    <h2>{{ '3rd Party' | translate }}</h2>
    <ion-button fill="clear"
                class="add-btn"
                *ngIf="!isSTMRReadonly(stmrHeader)"
                (click)="addThirdParty()">
      <ion-icon name="add-circle-outline"></ion-icon>
    </ion-button>
  </div>

  <div class="section-content">
    <div class="empty-message">
      <div class="crew-list" *ngIf="getNoOfThirdPartyCrewMembers(stmrCrew) === 0">
      <span class="normal-text">{{ 'No third party crew members added.' | translate }}</span>
    </div>
</div>
    <div class="crew-list" *ngIf="getNoOfThirdPartyCrewMembers(stmrCrew) > 0">
      <div class="crew-item third-party-crew-item"
           *ngFor="let crew of stmrCrew; let i = index"
           [style.display]="crew.CREW_TYPE !== 'THIRD_PARTY' || crew.P_MODE === 'D' ? 'none' : 'block'">
        <div class="crew-input-section">
          <div class="crew-input-group">
            <span class="label">{{ 'Name' | translate }}:</span>
            <input type="text"
                   class="detail-input crew-name-input"
                   [(ngModel)]="crew.CREW_NAME"
                   (ngModelChange)="onThirdPartyCrewNameChange(crew, i)"
                   placeholder="Name"
                   [disabled]="isSTMRReadonly(stmrHeader)">
          </div>
          <div class="crew-signature-section">
            <ion-button *ngIf="!crew.CREW_SIGN"
                        fill="outline"
                        color="danger"
                        class="signature-btn"
                        (click)="captureSign('CREW_SIGN', crew)"
                        [disabled]="!crew.CREW_NAME || crew.CREW_NAME.trim().length === 0">
              {{ 'Tap to Add Signature' | translate }}
            </ion-button>
            <div *ngIf="crew.CREW_SIGN" class="signature-display" (click)="captureSign('CREW_SIGN', crew)">
              <img [src]="crew.CREW_SIGN" alt="Crew Signature" class="signature-image" />
              <p class="signature-label">{{ 'Tap to Edit Signature' | translate }}</p>
            </div>
          </div>
          <ion-button fill="clear"
                      color="medium"
                      class="remove-btn"
                      (click)="removeCrewMember(crew)"
                      *ngIf="!isSTMRReadonly(stmrHeader)">
            <ion-icon name="close-outline"></ion-icon>
          </ion-button>
        </div>
      </div>
    </div>
  </div>

  <!-- Crew Supervisor Section -->
  <div class="section-header">
    <h2>{{ 'Crew Supervisor' | translate }}</h2>
  </div>

  <div class="section-content supervisor-section">
    <div class="supervisor-input">
      <span class="label" [ngClass]="{'error-text': !stmrHeader.ONSITE_SUP}">{{ 'Name' | translate }}:</span>
      <div class="input-with-dropdown">
        <input type="text"
               class="detail-input"
               [ngClass]="{'error-input': !stmrHeader.ONSITE_SUP}"
               [(ngModel)]="stmrHeader.ONSITE_SUP"
               (ngModelChange)="onModelChange($event)"
               (focus)="openSelectCrew('ONSITE_SUP', stmrHeader.ONSITE_SUP, stmrHeader.ONSITE_SUP)"
               [disabled]="isSTMRReadonly(stmrHeader)"
               placeholder="Name">
        <ion-button fill="clear"
                    class="dropdown-btn"
                    *ngIf="!isSTMRReadonly(stmrHeader)"
                    (click)="openSelectCrew('ONSITE_SUP', stmrHeader.ONSITE_SUP, stmrHeader.ONSITE_SUP)">
          <ion-icon name="chevron-down-outline"></ion-icon>
        </ion-button>
      </div>
    </div>

    <div class="supervisor-signature">
      <ion-button *ngIf="!stmrHeader.ONSITE_SUP_SIGN"
                  fill="outline"
                  color="danger"
                  class="signature-btn"
                  (click)="captureOnsiteSign('ONSITE_SUP_SIGN')">
        {{ 'Tap to Add Signature' | translate }}
      </ion-button>
      <div *ngIf="stmrHeader.ONSITE_SUP_SIGN" class="signature-display" (click)="captureOnsiteSign('ONSITE_SUP_SIGN')">
        <img [src]="stmrHeader.ONSITE_SUP_SIGN" alt="Supervisor Signature" class="signature-image" />
        <p class="signature-label">{{ 'Tap to Edit Signature' | translate }}</p>
      </div>
    </div>
  </div>

</ion-content>
