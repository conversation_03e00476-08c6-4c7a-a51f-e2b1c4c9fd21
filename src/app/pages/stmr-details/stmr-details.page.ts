import { Component, <PERSON>Init, Ng<PERSON>one } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DataService } from 'src/app/services/data.service';
import { BusyIndicatorService } from 'src/app/services/busy-indicator.service';
import { UnviredCordovaSDK, ResultType, RequestType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { UtilityService } from 'src/app/services/utility.service';
import { PrintSTMRFormService } from 'src/app/services/printStmr.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  IonGrid,
  IonRow,
  IonCol,
  IonLabel,
  IonInput,
  IonButton,
  IonIcon,
  IonCard,
  IonCardHeader,
  IonCardContent,
  IonCardTitle,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButtons,
  IonContent,
  IonItem,
  IonRadio,
  IonRadioGroup,
  IonThumbnail,
  ModalController,
  AlertController,
  IonSearchbar,
  ToastController,
  PopoverController
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { AppConstants } from 'src/app/constants/appConstants';
import { SelectListPage } from '../select-list/select-list.page';
import { TopicsPage } from '../topics/topics.page';
import {
  personCircleOutline,
  sendOutline,
  chevronDownOutline,
  arrowBackOutline,
  cogOutline,
  trashOutline,
  personAddOutline,
  closeOutline,
  addCircleOutline
} from 'ionicons/icons';
import { STMR_ACTION } from 'src/models/STMR_ACTION';
import { SignaturePage } from '../signature/signature.page';
import { STMR_TOPIC } from 'src/models/STMR_TOPIC';
import { STMR_HSE_TOPIC } from 'src/models/STMR_HSE_TOPIC';
import { STMR_CTA_TOPIC } from 'src/models/STMR_CTA_TOPIC';
import { CTA_HEADER } from 'src/models/CTA_HEADER';
import { STMR_FORM } from 'src/models/STMR_FORM';
import { HSE_STANDARD_HEADER } from 'src/models/HSE_STANDARD_HEADER';
import { StmrOptionsPopoverPage } from '../stmr-options-popover/stmr-options-popover.page';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { filter, take } from 'rxjs/operators';
import { selectPrefilledData } from 'src/app/store/store.selector';
import { CrewPage } from '../crew/crew.page';
import { CREW_HEADER } from 'src/models/CREW_HEADER';
import { STMR_HEADER } from 'src/models/STMR_HEADER';
import { STMR_CREW } from 'src/models/STMR_CREW';
import { TEMPLATE_HEADER } from 'src/models/TEMPLATE_HEADER';
import { AlertService } from 'src/app/services/alert.service';
import { STMR_TOPIC_ENTITY } from 'src/models/STMR_TOPIC_ENTITY';
import { STMR_FORM_DATA } from 'src/models/STMR_FORM_DATA';
import moment from 'moment';

addIcons({
  'person-circle': personCircleOutline,
  'send-outline': sendOutline,
  'chevron-down-outline': chevronDownOutline,
  'arrow-back-outline': arrowBackOutline,
  'cog-outline': cogOutline,
  'trash-outline': trashOutline,
  'person-add-outline': personAddOutline,
  'close-outline': closeOutline,
  'add-circle-outline': addCircleOutline
});

@Component({
  selector: 'app-stmr-details',
  templateUrl: './stmr-details.page.html',
  styleUrls: ['./stmr-details.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IonGrid,
    IonRow,
    IonCol,
    IonLabel,
    IonInput,
    IonButton,
    IonIcon,
    IonCard,
    IonCardHeader,
    IonCardContent,
    IonCardTitle,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonContent,
    IonItem,
    IonRadio,
    IonRadioGroup,
    IonThumbnail,
    SelectListPage,
    IonSearchbar,
  ]
})
export class STMRDetailsPage implements OnInit {
  stmrHeader: any = {};
  stmrTopicEntity: any[] = [];
  stmrCrew: any[] = [];
  isSTMRComplete: boolean = false;
  styleTheme: string = '';
  isLoading: boolean = false;
  loadingMessage: string = '';
  isSTMRUpdated = false;
  timestampOfLastTap: number = 0;
  isPageActive = true;
  prefillData: any;
  autoFillData: any = {};
  stmrActions: STMR_ACTION[] = [];
  isNewSTMR: boolean = false;
  linkedForms: any;
  templates: TEMPLATE_HEADER[] = [];
  isPopOverOpen: boolean = AppConstants.BOOL_FALSE;

  constructor(
    private modalController: ModalController,
    private alertController: AlertController,
    private translate: TranslateService,
    private ngZone: NgZone,
    private dataService: DataService,
    private unviredSDK: UnviredCordovaSDK,
    private busyIndicatorService: BusyIndicatorService,
    private toastController: ToastController,
    private UtilityService: UtilityService,
    private popoverCtrl: PopoverController,
    private printSTMRForm: PrintSTMRFormService,
    private router: Router,
    private alertService: AlertService,
    private busyIndicator: BusyIndicatorService,
    private store: Store<any>
  ) { }
prefillData$ = this.store.select(selectPrefilledData);
  async ngOnInit() {
    // this.store.select(selectPrefilledData)
    //   .pipe(filter(data => !!data)) // only if data exists
    //   .subscribe(data => {
    //     this.handlePrefill(data);
    //   });
      this.prefillData$.pipe(take(1)).subscribe(data => { 
        this.handlePrefill(data);
  });
  await this.getAllData();
  }

  async getAllData() {
    try {
      // Crew + Topics + Forms
      this.stmrCrew = await this.dataService.getCrewMembers(this.stmrHeader.STMR_ID);
      const topics = await this.dataService.getTopics(this.stmrHeader.STMR_ID);
      const forms = await this.dataService.getForms(this.stmrHeader.STMR_ID);
      const formData = await this.dataService.getFormData(this.stmrHeader.STMR_ID);
      this.stmrActions = await this.dataService.getFormActions(this.stmrHeader.STMR_ID);

      // Build Topic Entities
      const topicHashList: Record<string, STMR_TOPIC_ENTITY> = {};
      topics.forEach(topic => {
        const topicEntity = new STMR_TOPIC_ENTITY();
        topicEntity.topic = topic;
        topicHashList[topic.TOPIC_NO] = topicEntity;
      });

      forms.forEach(form => {
        topicHashList[form.TOPIC_NO]?.forms.push(form);
      });

      formData.forEach(fd => {
        topicHashList[fd.TOPIC_NO]?.data.push(fd);
      });

      this.stmrTopicEntity = Object.values(topicHashList);

      // Reload header
      try {
        this.stmrHeader = await this.dataService.reloadHeader(this.stmrHeader.LID);
      } catch (err: any) {
        if (err.message.includes('No STMR Header')) {
          await this.dataService['unviredSDK'].logInfo('STMRDetailsPage', 'getAllData', 'Creating a new STMR.');
        }
      }

    } catch (error: any) {
      this.alertService.showAlert('Error', error.message || 'Unexpected error');
      await this.dataService['unviredSDK'].logError('STMRDetailsPage', 'getAllData', error.message);
    }
  }

  private handlePrefill(data: any) {
    console.log('Prefill data in component (from store):', data);
    this.autoFillData = data;
    this.initializeSTMRFromPrefill(data);
  }

  
   initializeSTMRFromPrefill(data: any) {
    this.stmrHeader = {
      STMR_ID : data.STMR_ID || 'New',
      COMPANY: data.COMPANY || '',
      COMP_CODE: data.COMP_CODE || data.COMPANY_CODE || data.COMPANY?.CODE || '',
      RIG_NO: data.RIG_NO || '',
      RIG_TYPE: data.RIG_TYPE || '',
      SHIFT: data.SHIFT || '',
      SHIFT_TIME: data.SHIFT_TIME || new Date().toISOString(),
      WELL_LOC: data.WELL_LOC || '',
      CHAIRED_BY: data.CHAIRED_BY || '',
      OPERATOR: {
        NAME: data.OPERATOR?.NAME || '',
        SIGN: data.OPERATOR?.SIGN || ''
      },
      ONSITE_SUP: data.ONSITE_SUP || '',
      ONSITE_SUP_SIGN: '',
      STMR_STATUS: '',
      SYNC_STATUS: '',
    };
  // after fetching prefills or data:
if (Array.isArray(data.OPERATOR) && data.OPERATOR.length > 0) {
  this.stmrHeader.OPERATOR = data.OPERATOR[0];
} else if (data.OPERATOR && typeof data.OPERATOR === 'object') {
  this.stmrHeader.OPERATOR = data.OPERATOR;
} else {
  this.stmrHeader.OPERATOR = { NAME: '' };
}
    
    this.stmrCrew = data.CREW ? [...data.CREW] : [];
    this.stmrTopicEntity = data.TOPICS ? [...data.TOPICS] : [];
    this.markSTMRAsPristine();
  }

  async addTopic() {
    const modal = await this.modalController.create({
      component: TopicsPage,
      cssClass: 'full-screen-modal',
      componentProps: { 
        topic: null,
        stmrHeader: this.stmrHeader,
       }
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data && data.topic) {
      this.stmrTopicEntity.push({ topic: data.topic, forms: [], data: [] });
      this.markSTMRAsUpdated();
    }
  }

  async editTopic(entity: any, i: number) {
    const modal = await this.modalController.create({
      component: TopicsPage,
      cssClass: 'full-screen-modal',
      componentProps: { 
      topic: entity.topic,
      stmrHeader: this.stmrHeader,  }
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data && data.topic) {
      this.stmrTopicEntity[i].topic = data.topic;
      this.markSTMRAsUpdated();
    }
  }

  async removeTopic(topic: any, idx: number) {
    const alert = await this.alertController.create({
      header: 'Delete Topic',
      message: `Are you sure you want to delete this topic?`,
      buttons: [
        { text: 'Cancel', role: 'cancel' },
        { text: 'Delete', role: 'destructive', handler: () => {
            this.stmrTopicEntity[idx].topic.P_MODE = 'D';
            this.markSTMRAsUpdated();
          } }
      ]
    });
    await alert.present();
  }

  async addCrew() {
  const modal = await this.modalController.create({
    component: CrewPage,
    cssClass: 'full-screen-modal',
    componentProps: {
      crewData: this.stmrCrew,   // pass current crew
      theme: this.styleTheme,
      RIG_NO: this.stmrHeader?.RIG_NO
    }
  });

  await modal.present();

  const { data, role } = await modal.onWillDismiss();
  if (role === 'confirm' && data?.selectedCrew) {
    // Replace current crew with new selection
    this.stmrCrew = data.selectedCrew.map((c: any) => ({
      ...c,
      P_MODE: 'A'  // mark as added
    }));

    this.markSTMRAsUpdated();
  }
}


  async editCrew(crew: any, i: number) {
    const modal = await this.modalController.create({
      component: CrewPage,
      cssClass: 'full-screen-modal',
      componentProps: { crew }
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data && data.crew) {
      this.stmrCrew[i] = { ...data.crew, P_MODE: 'M' };
      this.markSTMRAsUpdated();
    }
  }



  /**
 * Open Crew select modal
 */
public async openSelectCrew(field: string, userName?: string, searchValue?: string): Promise<void> {
  if (this.isPopOverOpen) return;
  await this.showMembers(field, userName, searchValue);
}

/**
 * Show members from CREW and open modal
 */
public async showMembers(field: string, userName?: string, searchValue?: string): Promise<void> {
  if (this.isPopOverOpen) return;

  // Construct crew list data
  const listData = this.autoFillData.CREW.map((crew: any) => {
    let displayString = crew.USER_NAME;
    if (crew.SOURCE === 'SAP') {
      displayString += ` - ${crew.DESIGNATION} - ${crew.PERSON_NO}`;
    } else {
      displayString += ` - ${crew.DESIGNATION} - ${crew.SOURCE}`;
    }
    return {
      DisplayString: displayString,
      object: crew,
      isSelected: crew.USER_NAME === userName ? AppConstants.BOOL_TRUE : AppConstants.BOOL_FALSE
    };
  });

  // Open modal
  const crewModal = await this.modalController.create({
    component: SelectListPage,
    componentProps: {
      listData,
      listDetails: {
        title: `Select Crew Member`,
        searchPlaceHolder: `Search / Type a New Crew Member`,
        selectKey: 'isSelected',
        multiSelect: AppConstants.BOOL_FALSE,
        searchValue
      },
      theme: this.styleTheme
    },
    cssClass: 'custom-modal',
    backdropDismiss: false
  });

  this.isPopOverOpen = AppConstants.BOOL_TRUE;
  await crewModal.present();

  const result = await crewModal.onDidDismiss();
  this.isPopOverOpen = AppConstants.BOOL_FALSE;

  const previousValue = this.stmrHeader[field];

  if (result?.data?.DisplayString && result.data.object) {
    this.ngZone.run(() => {
      this.stmrHeader[field] = result.data.object.USER_NAME || result.data.DisplayString;
    });
  } else if (result?.data?.newEntry) {
    this.stmrHeader[field] = result.data.value || "";
  }

  // Clear Crew Supervisor Signature if crew member is changed
  if (field === 'ONSITE_SUP' && previousValue !== this.stmrHeader[field]) {
    this.stmrHeader.ONSITE_SUP_SIGN = '';
  }

  this.markSTMRAsUpdated();
}


  close() {
    this.modalController.dismiss();
    this.router.navigate(['/forms']);
  }

  async presentSettingsPopover(event: UIEvent): Promise<void> {
  const popover = await this.popoverCtrl.create({
    component: StmrOptionsPopoverPage,
    event,
    translucent: true
  });

  await popover.present();

  const { data } = await popover.onDidDismiss();

  if (!data) {
    return;
  }

  if (data.changeTheme) {
    this.styleTheme = this.styleTheme === 'normal' ? 'contrast' : 'normal';
  } else if (data.print) {
    try {
      // Extract topics from stmrTopicEntity
      const topics: STMR_TOPIC[] = this.stmrTopicEntity.map(entity => entity.topic);

      const STMRTopicsWithHSENames = await this.updateHSENamesForSTMRTopics(topics);
      const STMRTopicsWithCTANames = await this.updateCTANamesForSTMRTopics(STMRTopicsWithHSENames);
      const STMRTopicWithCTAForms = await this.updateFormsForCTATopic(STMRTopicsWithCTANames);

      // Print STMR
      this.printSTMRForm.printSTMRForm(this.stmrHeader.STMR_ID, {
        stmrHeader: this.stmrHeader,
        stmrTopic: STMRTopicWithCTAForms,
        stmrCrew: this.stmrCrew
      });

    } catch (error) {
      console.error('Error while preparing STMR for print:', error);
    }
  }
}

async updateHSENamesForSTMRTopics(stmrTopics: STMR_TOPIC[]): Promise<STMR_HSE_TOPIC[]> {
  if (!stmrTopics.length) return [];

  const HSEIds = stmrTopics.map(item => item.STD_ID).filter(Boolean);

  if (!HSEIds.length) {
    return stmrTopics.map(item => new STMR_HSE_TOPIC(item));
  }

  const inClause = HSEIds.map(id => `'${id}'`).join(',');
  const query = `SELECT * FROM HSE_STANDARD_HEADER WHERE STD_ID IN (${inClause})`;

  const result = await this.unviredSDK.dbExecuteStatement(query);

  if (result.type !== ResultType.success) {
    console.error('Failed to fetch HSE headers');
    return stmrTopics.map(item => new STMR_HSE_TOPIC(item));
  }
  const hseMap: Map<string, string> = new Map<string, string>(
  result.data.map((hse: HSE_STANDARD_HEADER) => [hse.STD_ID, hse.NAME])
 );
  return stmrTopics.map(item => {
    const hseTopic = new STMR_HSE_TOPIC(item);
    hseTopic.STD_TYPE = hseMap.get(item.STD_ID) || '';
    return hseTopic;
  });
}

async updateCTANamesForSTMRTopics(stmrTopics: STMR_TOPIC[]): Promise<STMR_CTA_TOPIC[]> {
  if (!stmrTopics.length) return [];

  const CTAIds = stmrTopics.map(item => item.CTA_ID).filter(Boolean);

  if (!CTAIds.length) {
    return stmrTopics.map(item => new STMR_CTA_TOPIC(item));
  }

  const inClause = CTAIds.map(id => `'${id}'`).join(',');
  const query = `SELECT * FROM CTA_HEADER WHERE CTA_ID IN (${inClause})`;

  const result = await this.unviredSDK.dbExecuteStatement(query);

  if (result.type !== ResultType.success) {
    console.error('Failed to fetch CTA headers');
    return stmrTopics.map(item => new STMR_CTA_TOPIC(item));
  }

  const ctaMap: Map<string, string> = new Map<string, string>(
  result.data.map((cta: CTA_HEADER) => [cta.CTA_ID, cta.NAME])
);


  return stmrTopics.map(item => {
    const ctaTopic = new STMR_CTA_TOPIC(item);
    ctaTopic.CTA_TYPE = ctaMap.get(item.CTA_ID) || '';
    return ctaTopic;
  });
}

async updateFormsForCTATopic(stmrWithCTATopics: STMR_CTA_TOPIC[]): Promise<STMR_CTA_TOPIC[]> {
  if (!stmrWithCTATopics.length) return [];

  const stmrId = stmrWithCTATopics[0].STMR_ID;

  const result = await this.unviredSDK.dbSelect("STMR_FORM", `STMR_ID = '${stmrId}'`);

  if (result.type !== ResultType.success || !result.data?.length) {
    console.error("Error fetching forms for CTA topics or no data");
    return stmrWithCTATopics;
  }

  const forms: STMR_FORM[] = result.data;

  stmrWithCTATopics.forEach(topic => {
    const formsForTopic = forms.filter(form => form.TOPIC_NO === topic.TOPIC_NO);
    topic.FORMS = formsForTopic.map(f => f.NAME);
  });

  return stmrWithCTATopics;
}
  
  async localSaveSTMR(): Promise<void> {
    const now = Date.now();
    if (now - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
      this.unviredSDK.logInfo('STMRDetailsPage', 'localSaveSTMR', 'Ignoring double tap');
      return;
    }
    this.timestampOfLastTap = now;

    if (this.stmrHeader.SYNC_STATUS === AppConstants.SYNC_STATUS.QUEUED) {
      // Save to server queue directly if already queued
      await this.saveSTMRToServer();
      return;
    }

    try {
      await this.busyIndicatorService.displayBusyIndicator(this.translate.instant("Saving STMR. Please wait..."));
      await this.prepareAndSaveSTMR();
      const toast = await this.toastController.create({
        message: this.translate.instant('STMR Changes saved successfully'),
        duration: 1000,
        position: 'bottom'
      });
      await toast.present();
      this.markSTMRAsPristine();
    } catch (error:any) {
      await this.busyIndicatorService.hideBusyIndicator();
      this.unviredSDK.logError('STMRDetailsPage', 'localSaveSTMR', 'Error while saving STMR to DB');
      await this.showAlert(this.translate.instant("Error"), this.translate.instant("Error while saving STMR: ") + error);
      throw error;
    }
  }

  /**
   * Save STMR into the server queue and submit the data
   */
  async saveSTMRToServer(): Promise<void> {
    try {
      await this.busyIndicatorService.displayBusyIndicator(this.translate.instant("Queueing STMR to server. Please wait..."));
      await this.prepareAndSaveSTMR();

      await this.submitToServer();

      await this.busyIndicatorService.hideBusyIndicator();
      this.markSTMRAsPristine();
    } catch (error) {
      await this.busyIndicatorService.hideBusyIndicator();
      this.unviredSDK.logError('STMRDetailsPage', 'saveSTMRToServer', 'Error while queueing STMR to server');
      await this.showAlert(
        this.translate.instant("Error"),
        this.translate.instant("Error while submitting STMR to server: ") + JSON.stringify(error, null, 2)
      );
      throw error;
    }
  }

  async saveAndExitSTMR(): Promise<void> {
    const now = Date.now();
    if (now - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
      this.unviredSDK.logInfo("STMRDetailsPage", "saveAndExitSTMR", "Ignoring double tap");
      return;
    }
    this.timestampOfLastTap = now;
    this.isPageActive = false;

    try {
      await this.busyIndicatorService.displayBusyIndicator(this.translate.instant("Queueing STMR to server. Please wait..."));
      await this.prepareAndSaveSTMR();
      await this.submitToServer();
      await this.busyIndicatorService.hideBusyIndicator();
      this.goToFormsPage();
    } catch (error) {
      this.isPageActive = true;
      await this.busyIndicatorService.hideBusyIndicator();
      this.unviredSDK.logError("STMRDetailsPage", "saveAndExitSTMR", "Error while queueing STMR to server");
      await this.showAlert(
        this.translate.instant("Error"),
        this.translate.instant("Error while submitting STMR to server: ") + JSON.stringify(error, null, 2)
      );
    }
  }

  /**
   * Prepare and save STMR only if it is not already sent (check using Unvired SDK)
   */
  prepareAndSaveSTMR(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.unviredSDK.logInfo("STMRDetailsPage", "prepareAndSaveSTMR", "Checking if STMR is in sent items.");
      this.unviredSDK.isInSentItem(this.stmrHeader.LID).then(resultInSent => {
        const isSent = (String(resultInSent.data).toLowerCase() === "true" || resultInSent.data === AppConstants.BOOL_TRUE);
        if (isSent) {
          this.unviredSDK.logInfo("STMRDetailsPage", "prepareAndSaveSTMR", "STMR is already sent to server.");
          this.unviredSDK.getMessages(); 
          this.showAlert(
            this.translate.instant("Previous request still being reconciled!"),
            this.translate.instant("STMR could not be submitted. Please wait till response is received.")
          );
          reject('Already sent');
        } else {
          this.unviredSDK.logInfo("STMRDetailsPage", "prepareAndSaveSTMR", "Saving STMR with latest changes.");
          this.saveSTMRIntoDB().then(() => resolve()).catch((err:any) => reject(err));
        }
      }).catch(err => {
        this.unviredSDK.logError("STMRDetailsPage", "prepareAndSaveSTMR", err);
        reject(err);
      });
    });
  }

  /**
 * Save STMR Header and Items into DB (async/await + unviredSdk)
 */
async saveSTMRIntoDB(): Promise<void> {
  try {
    this.unviredSDK.logInfo("STMRDetails", "saveSTMRIntoDB", "Initiating STMR Save...");

    // Update Header metadata before insert
    this.stmrHeader.LAST_SYNC_USER = this.autoFillData.USER_ID || "";
    this.stmrHeader.SUBM_BY = this.autoFillData.USER_ID || "";
    this.stmrHeader.TIME_ZONE = this.UtilityService.getTimezone();

    // Save Header
    await this.insertSTMR();

    // Save Items
    await this.insertSTMRItems();

    this.unviredSDK.logInfo(
      "STMRDetails",
      "saveSTMRIntoDB",
      "Successfully saved STMR header and items into DB."
    );

  } catch (error) {
    this.unviredSDK.logError(
      "STMRDetails",
      "saveSTMRIntoDB",
      `Error saving STMR into DB: ${JSON.stringify(error, null, 2)}`
    );
    throw error; // rethrow so caller can handle
  }
}

/**
 * Update STMR_HEADER and insert into DB
 */
public async insertSTMR(): Promise<void> {
  try {
    // Trim string fields
    if (this.stmrHeader.WELL_LOC) this.stmrHeader.WELL_LOC = this.stmrHeader.WELL_LOC.trim();
    if (this.stmrHeader.CHAIRED_BY) this.stmrHeader.CHAIRED_BY = this.stmrHeader.CHAIRED_BY.trim();
    if (this.stmrHeader.ONSITE_MGR) this.stmrHeader.ONSITE_MGR = this.stmrHeader.ONSITE_MGR.trim();
    if (this.stmrHeader.ONSITE_SUP) this.stmrHeader.ONSITE_SUP = this.stmrHeader.ONSITE_SUP.trim();

    // Set completion date and metadata
    const dateComp = moment.utc().unix();
    this.stmrHeader.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
    this.stmrHeader.DATE_COMP = dateComp;

    // Use prefill data from store/service
    const data = this.prefillData; // or get from observable if reactive
    this.stmrHeader.LAST_SYNC_USER = data.USER_ID;
    this.stmrHeader.SUBM_BY = data.USER_ID;
    this.stmrHeader.TIME_ZONE = this.UtilityService.getTimezone();

    // Remove unnecessary properties
    delete this.stmrHeader.stmrStatus;
    delete this.stmrHeader.syncStatus;
    delete this.stmrHeader.isSelected;

    // Insert/Update via Unvired SDK
    const result = await this.unviredSDK.dbInsertOrUpdate(
      AppConstants.STMR_HEADER,
      this.stmrHeader,
      true
    );

    if (result.type !== ResultType.success) {
      this.busyIndicator.hideBusyIndicator();
      this.alertService.showAlert("Error", result.message || "Failed to insert STMR Header");
      throw result;
    }

    this.unviredSDK.logInfo("StmrDetailsPage", "insertSTMR", "STMR Header inserted successfully");
  } catch (error) {
    this.unviredSDK.logError("StmrDetailsPage", "insertSTMR", `Error inserting STMR Header: ${JSON.stringify(error)}`);
    throw error;
  }
}

/**
 * Insert STMR Items (crew, topics, forms, actions)
 */
public async insertSTMRItems(): Promise<void> {
  try {
    // Deep copy crew array to avoid mutations
    const crew = JSON.parse(JSON.stringify(this.stmrCrew));
    await this.insertSTMRCrew(crew); // insertSTMRCrew should be converted to async/await as well
  } catch (error) {
    this.unviredSDK.logError("StmrDetailsPage", "insertSTMRItems", `Error inserting STMR Items: ${JSON.stringify(error)}`);
    throw error;
  }
}
/**
 * 2a. Insert STMR_CREW
 * After STMR_CREW, insert STMR_TOPIC
 */
async insertSTMRCrew(tempCrew: any[]): Promise<void> {
  if (tempCrew.length > 0) {
    // Skip GLOBAL
    if (tempCrew[0].OBJECT_STATUS === AppConstants.OBJECT_STATUS.GLOBAL) {
      tempCrew.splice(0, 1);
      return this.insertSTMRCrew(tempCrew);
    }

    await this.unviredSDK.logInfo('stmrDetailsPage', 'insertStmrCrew',
      `inserting new STMR crew into the database: ${JSON.stringify(tempCrew[0], null, 2)}`);

    try {
      await this.unviredSDK.dbInsertOrUpdate(AppConstants.TABLE_STMR_CREW, tempCrew[0], AppConstants.BOOL_FALSE);
      await this.updateSTMRHeaderLastSync(this.stmrHeader);
      tempCrew.splice(0, 1);
      return this.insertSTMRCrew(tempCrew);
    } catch (result: any) {
      this.busyIndicator.hideBusyIndicator();
      this.alertService.showAlert('Error', `${result.error} ${result.message}`);
      await this.unviredSDK.logError('stmrDetailsPage', 'insertSTMRCrew',
        `Inserting STMR Crew into database failed: ${result.message} ${result.error}`);
      throw result;
    }
  } else {
    // Send a copy (original gets modified later)
    const stmrTopicEntityCopy: STMR_TOPIC_ENTITY[] = JSON.parse(JSON.stringify(this.stmrTopicEntity));
    await this.insertSTMRTopic(stmrTopicEntityCopy, [], [], []);
  }
}

/**
 * 2b. Insert STMR_TOPIC
 * Builds arrays for CTA_ID and HSE (STD_ID); then proceeds to STMR_FORM
 */
async insertSTMRTopic(
  tempTopic: STMR_TOPIC_ENTITY[],
  ctaIdArr: string[] = [],
  hseIdArr: string[] = [],
  formArray: Array<{ CTA_ID: string; TOPIC_NO: string }> = []
): Promise<void> {
  if (tempTopic.length > 0) {
    const itemStmrTopic: STMR_TOPIC = tempTopic[0].topic;

    if (itemStmrTopic) {
      if (!ctaIdArr) ctaIdArr = [];
      ctaIdArr.push(itemStmrTopic.CTA_ID);

      if (!hseIdArr) hseIdArr = [];
      hseIdArr.push(itemStmrTopic.STD_ID);

      if (!formArray) formArray = [];
      formArray.push({
        CTA_ID: itemStmrTopic.CTA_ID,
        TOPIC_NO: itemStmrTopic.TOPIC_NO
      });
    }

    // Skip GLOBAL
    if (itemStmrTopic.OBJECT_STATUS === AppConstants.OBJECT_STATUS.GLOBAL) {
      tempTopic.splice(0, 1);
      return this.insertSTMRTopic(tempTopic, ctaIdArr, hseIdArr, formArray);
    }

    await this.unviredSDK.logInfo('stmrDetailsPage', 'insertSTMRTopic',
      `Updating STMR Topic in the database... ${JSON.stringify(itemStmrTopic, null, 2)}`);

    try {
      await this.unviredSDK.dbInsertOrUpdate(AppConstants.TABLE_STMR_TOPIC, itemStmrTopic, AppConstants.BOOL_FALSE);
      await this.unviredSDK.logInfo('stmrDetailsPage', 'insertSTMRTopic',
        'Done. Inserting new STMR Topic into the database...');
      tempTopic.splice(0, 1);
      return this.insertSTMRTopic(tempTopic, ctaIdArr, hseIdArr, formArray);
    } catch (result: any) {
      this.busyIndicator.hideBusyIndicator();
      this.alertService.showAlert('Error', `${result.error} ${result.message}`);
      await this.unviredSDK.logError('stmrDetailsPage', 'insertSTMRTopic',
        `Inserting STMR Topic into database failed: ${result.message} ${result.error}`);
      throw result;
    }
  } else {
    // Collect all forms from topic entities and proceed
    let stmrForms: STMR_FORM[] = [];
    this.stmrTopicEntity.forEach(e => (stmrForms = stmrForms.concat(e.forms)));
    await this.insertSTMRForms(stmrForms);
  }
}

/**
 * Insert STMR_FORM
 * After STMR_FORM, proceed to STMR_FORM_DATA
 */
async insertSTMRForms(stmrForms: STMR_FORM[]): Promise<void> {
  if (stmrForms.length === 0) {
    // Move to Form Data
    let stmrData: STMR_FORM_DATA[] = [];
    this.stmrTopicEntity.forEach(e => (stmrData = stmrData.concat(e.data)));
    await this.insertSTMRFormData(stmrData);
    return;
  }

  // Skip GLOBAL
  if (stmrForms[0].OBJECT_STATUS === AppConstants.OBJECT_STATUS.GLOBAL) {
    stmrForms.splice(0, 1);
    return this.insertSTMRForms(stmrForms);
  }

  // Mutate as original (remove view-only flags)
  const formToSave = { ...stmrForms[0] };
  if ((formToSave as any)['syncStatus']) delete (formToSave as any)['syncStatus'];
  if ((formToSave as any)['formStatus']) delete (formToSave as any)['formStatus'];

  try {
    await this.unviredSDK.dbInsertOrUpdate(AppConstants.TABLE_STMR_FORM, formToSave, false);
    await this.unviredSDK.logInfo('stmrDetailsPage', 'insertSTMRForms',
      'Done. Inserting / Updating STMR Form.');
    stmrForms.splice(0, 1);
    await this.insertSTMRForms(stmrForms);
  } catch (result: any) {
    await this.unviredSDK.logError('stmrDetailsPage', 'insertSTMRForms',
      `inserting / Updating STMR Form into database failed: ${result.message} ${result.error}`);
    throw result;
  }
}

/**
 * Insert STMR_FORM_DATA
 * After STMR_FORM_DATA, proceed to STMR_ACTION
 */
async insertSTMRFormData(stmrFormData: STMR_FORM_DATA[]): Promise<void> {
  if (stmrFormData.length === 0) {
    await this.insertSTMRFormAction(this.stmrActions);
    return;
  }

  // Skip GLOBAL
  if (stmrFormData[0].OBJECT_STATUS === AppConstants.OBJECT_STATUS.GLOBAL) {
    stmrFormData.splice(0, 1);
    return this.insertSTMRFormData(stmrFormData);
  }

  try {
    await this.unviredSDK.dbInsertOrUpdate(AppConstants.TABLE_STMR_FORM_DATA, stmrFormData[0], false);
    await this.unviredSDK.logInfo('stmrDetailsPage', 'insertSTMRFormData',
      'Done. Inserting / Updating STMR Form Data.');
    stmrFormData.splice(0, 1);
    await this.insertSTMRFormData(stmrFormData);
  } catch (result: any) {
    await this.unviredSDK.logError('stmrDetailsPage', 'insertSTMRFormData',
      `Inserting STMR Form data into database failed: ${result.message} ${result.error}`);
    throw result;
  }
}

/**
 * Insert STMR_ACTION
 */
async insertSTMRFormAction(stmrActions: STMR_ACTION[]): Promise<void> {
  if (stmrActions.length === 0) {
    return;
  }

  // Skip GLOBAL
  if (stmrActions[0].OBJECT_STATUS === AppConstants.OBJECT_STATUS.GLOBAL) {
    stmrActions.splice(0, 1);
    return this.insertSTMRFormAction(stmrActions);
  }

  try {
    await this.unviredSDK.dbInsertOrUpdate(AppConstants.TABLE_STMR_ACTION, stmrActions[0], false);
    await this.unviredSDK.logInfo('stmrDetailsPage', 'insertSTMRFormAction',
      'Done. Inserting / Updating STMR Form Action.');
    stmrActions.splice(0, 1);
    await this.insertSTMRFormAction(stmrActions);
  } catch (result: any) {
    await this.unviredSDK.logError('stmrDetailsPage', 'insertSTMRFormAction',
      `Inserting STMR Form action into database failed: ${result.message} ${result.error}`);
    throw result;
  }
}

/**
 * 2c. Fetch Linked Templates with CTA type linked to CTA
 */
async fetchLinkedForms(
  ctaIdArr: string[],
  linkedForms: any[],
  formArray: Array<{ CTA_ID: string; TOPIC_NO: string }>
): Promise<any> {
  let tmp: any = {};

  if (formArray.length > 0) {
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_CTA_TMPLT, { CTA_ID: formArray[0].CTA_ID });
      if (result?.data?.length > 0) {
        if (!linkedForms) linkedForms = [];
        for (let i = 0; i < result.data.length; i++) {
          tmp = { ...result.data[i] };
          tmp['TOPIC_NO'] = formArray[0].TOPIC_NO;
          linkedForms.push(tmp);
        }
      }
      ctaIdArr.splice(0, 1);
      formArray.splice(0, 1);
      return this.fetchLinkedForms(ctaIdArr, linkedForms, formArray);
    } catch (result: any) {
      this.busyIndicator.hideBusyIndicator();
      this.alertService.showAlert('Error', `${result.error} ${result.message}`);
      await this.unviredSDK.logError('stmrDetailsPage', 'fetchLinkedForms',
        `Error while fetching Linked Templates from DB : ${result.message} ${result.error}`);
      throw result;
    }
  } else {
    // Build topics list
    const topics: STMR_TOPIC[] = this.stmrTopicEntity.map(entity => entity.topic);

    // All forms fetched construct STMR_FORM array
    const tmpLinkedForms = JSON.parse(JSON.stringify(linkedForms));
    const tmpStmrTopic = JSON.parse(JSON.stringify(topics));
    this.linkedForms = JSON.parse(JSON.stringify(linkedForms));
    return this.fetchTemplates(tmpLinkedForms, tmpStmrTopic);
  }
}

/**
 * 2d. Fetch Templates (released version), annotate linkedForms, then insert STMR_FORM
 */
async fetchTemplates(linkedForms: any[], strmrTopic: any[]): Promise<any> {
  if (linkedForms.length > 0) {
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_TEMPLATE_VERSION, { STATUS: 'REL' });
      if (result?.data?.length > 0) {
        this.templates = result.data;
        // Join released versions onto linkedForms
        for (let i = 0; i < result.data.length; i++) {
          for (let j = 0; j < linkedForms.length; j++) {
            if (result.data[i].TMPLT_ID === linkedForms[j].TEMPLATE_ID) {
              linkedForms[j].VER_ID = result.data[i].VER_ID;
              linkedForms[j].CRTD_BY = result.data[i].CRTD_BY;
              linkedForms[j].CRTD_ON = result.data[i].CRTD_ON;
            }
          }
        }
      }
      const tmpLinkedForms = JSON.parse(JSON.stringify(linkedForms));
      this.linkedForms = JSON.parse(JSON.stringify(linkedForms));
      return this.insertSTMRForm(tmpLinkedForms);
    } catch (result: any) {
      this.busyIndicator.hideBusyIndicator();
      this.alertService.showAlert('Error', `${result.error} ${result.message}`);
      await this.unviredSDK.logError('stmrDetailsPage', 'fetchTemplates',
        `Error while fetching Linked Templates from DB : ${result.message} ${result.error}`);
      throw result;
    }
  } else {
    const tmpLinkedForms = JSON.parse(JSON.stringify(linkedForms));
    this.linkedForms = JSON.parse(JSON.stringify(linkedForms));
    return this.insertSTMRForm(tmpLinkedForms);
  }
}

/**
 * 2e. Insert STMR_FORM
 * After STMR_FORM, insert STMR_FORM_DATA
 */
async insertSTMRForm(tempTmplt: any[]): Promise<void> {
  if (tempTmplt.length > 0) {
    let itemStmrForm: STMR_FORM = {} as STMR_FORM;
    let itemStmrFormData: STMR_FORM_DATA = {} as STMR_FORM_DATA;

    const formId = this.UtilityService.guid32();

    // STMR_FORM object (kept identical)
    itemStmrForm.STMR_ID = this.stmrHeader.STMR_ID;
    itemStmrForm.TOPIC_NO = tempTmplt[0].TOPIC_NO;
    itemStmrForm.FORM_ID = formId;
    itemStmrForm.VER_ID = tempTmplt[0].VER_ID;
    itemStmrForm.CRTD_BY = tempTmplt[0].CRTD_BY;
    itemStmrForm.CRTD_ON = moment.utc().unix();
    itemStmrForm.COMPANY = this.stmrHeader.COMPANY;
    itemStmrForm.RIG_NO = this.stmrHeader.RIG_NO;
    itemStmrForm.COMMENTS = '';
    itemStmrForm.FORM_STATUS = AppConstants.VAL_FORM_STATUS.OPEN;
    itemStmrForm.FID = this.stmrHeader.LID;
    itemStmrForm.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
    itemStmrForm.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;

    // STMR_FORM_DATA object (kept identical)
    itemStmrFormData.STMR_ID = this.stmrHeader.STMR_ID;
    itemStmrFormData.FORM_ID = formId;
    itemStmrFormData.TOPIC_NO = tempTmplt[0].TOPIC_NO;
    itemStmrFormData.DATA = '';
    itemStmrFormData.FID = this.stmrHeader.LID;
    itemStmrFormData.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
    itemStmrFormData.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;

    await this.unviredSDK.logInfo('stmrDetailsPage', 'insertSTMRForm', 'Inserting STMR Form into the database...');

    try {
      // NOTE: Your original used AppConstant.STMR_FORM / STMR_FORM_DATA here (not TABLE_*). Keeping it exactly.
      await this.unviredSDK.dbInsertOrUpdate(AppConstants.STMR_FORM, itemStmrForm, AppConstants.BOOL_FALSE);
      await this.unviredSDK.logInfo('stmrDetailsPage', 'insertSTMRForm',
        'Done. Inserting STMR Form into the database. Inserting STMR Form data...');

      await this.unviredSDK.dbInsertOrUpdate(AppConstants.STMR_FORM_DATA, itemStmrFormData, AppConstants.BOOL_FALSE);
      await this.unviredSDK.logInfo('stmrDetailsPage', 'insertSTMRForm',
        'Done. Inserting STMR Formdata into the database.');

      tempTmplt.splice(0, 1);
      return this.insertSTMRForm(tempTmplt);
    } catch (result: any) {
      this.busyIndicator.hideBusyIndicator();
      this.alertService.showAlert('Error', `${result.error} ${result.message}`);
      const where = !itemStmrFormData?.FORM_ID ? 'Form Header' : 'Form Header/Data';
      await this.unviredSDK.logError('stmrDetailsPage', 'insertSTMRForm',
        `Inserting ${where} into database failed: ${result.message} ${result.error}`);
      throw result;
    }
  } else {
    // Move to forms tab – identical behavior
    this.busyIndicator.hideBusyIndicator();
    return;
  }
}
  /**
   * Validation and submitting STMR
   */
  async submitSTMR(): Promise<void> {
    const now = Date.now();
    if (now - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
      this.unviredSDK.logInfo("STMRDetailsPage", "submitSTMR", "Ignoring double tap");
      return;
    }
    this.timestampOfLastTap = now;
    this.isPageActive = false;

    try {
      if (!this.validateSTMR(false, true)) {
        this.isPageActive = true;
        return;
      }

      if (!this.areAllFormsCompleted()) {
        this.isPageActive = true;
        await this.showAlert("Alert", "It seems like some forms are not filled completely.");
        return;
      }

      let stmrAction = this.createSTMRAction(this.stmrHeader);
      this.stmrActions = [...this.stmrActions, stmrAction];
      this.stmrHeader.STMR_STATUS = AppConstants.VAL_FORM_STATUS.SUBM;

      await this.busyIndicatorService.displayBusyIndicator(this.translate.instant("Submitting STMR. Please wait..."));
      
      await this.prepareAndSaveSTMR();
      await this.submitToServer();

      await this.busyIndicatorService.hideBusyIndicator();
      this.goToFormsPage();

    } catch (error) {
      this.isPageActive = true;
      await this.busyIndicatorService.hideBusyIndicator();
      this.unviredSDK.logError("STMRDetailsPage", "submitSTMR", "Error while submitting STMR to server");
      await this.showAlert(
        this.translate.instant("Error"),
        this.translate.instant("Error while submitting STMR to server: ") + JSON.stringify(error, null, 2)
      );
    }
  }

  async submitToServer(): Promise<void> {
  try {
    this.unviredSDK.logInfo(
      'StmrDetailsPage',
      'submitToServer',
      'Sending STMR Form to server in async mode.'
    );

    const result = await this.unviredSDK.syncBackground(
      RequestType.RQST,
      { "STMR_HEADER": this.stmrHeader }, // header payload
      '', // no item payload in this case
      AppConstants.PA_FORMS_STMR_SUBMIT,
      'STMR',
      this.stmrHeader.LID,
      false
    );

    if (result.type === ResultType.success) {
      this.unviredSDK.logInfo(
        'StmrDetailsPage',
        'submitToServer',
        'STMR Form submitted successfully.'
      );
    } else {
      this.unviredSDK.logError(
        'StmrDetailsPage',
        'submitToServer',
        `STMR Form submission failed: ${result.message} ${result.error}`
      );
      throw new Error(result.message ?? 'Unknown error');
    }
  } catch (error) {
    this.unviredSDK.logError(
      'StmrDetailsPage',
      'submitToServer',
      `Error while submitting STMR: ${JSON.stringify(error)}`
    );
    throw error;
  }
  }


  async goToFormsPage() {

  if (this.isNewSTMR) {
    // Reset stack → FormsPage as root
    await this.router.navigateByUrl('/forms', { replaceUrl: true });
  } else {
    // Just go back → either dismiss modal or navigate
    try {
      await this.modalController.dismiss(); // if you opened STMR in a modal
    } catch {
      await this.router.navigate(['/forms']);
    }
  }
}

  /**
 * Checks if all forms associated with topics are completed.
 * Completed = P_MODE !== 'D' AND FORM_STATUS is SUBM or SKIP
 */
areAllFormsCompleted(): boolean {
  return this.stmrTopicEntity.every(topic =>
    topic.forms.every((form: any) =>
      form.P_MODE === 'D' ||
      form.FORM_STATUS === AppConstants.VAL_FORM_STATUS.SUBM ||
      form.FORM_STATUS === AppConstants.VAL_FORM_STATUS.SKIP
    )
  );
}

  private createSTMRAction(stmrHeader: { STMR_ID: string; LID: string }): STMR_ACTION {
  this.markSTMRAsUpdated();

  const stmrActionObj = new STMR_ACTION();

  stmrActionObj.STMR_ID = stmrHeader.STMR_ID;
  stmrActionObj.FORM_ID = stmrHeader.STMR_ID;
  stmrActionObj.ACTION_CODE = AppConstants.ACTION_CODE.COMPLETE;
  stmrActionObj.P_MODE = 'A';
  stmrActionObj.FID = stmrHeader.LID;
  stmrActionObj.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;

  return stmrActionObj;
}

 validateSTMR(noAlert = false, isSubmit = false): boolean {
    let keyMissing = false, deleteCount = 0;

    if (!this.stmrHeader.SHIFT) {
      if (!noAlert) this.showAlert('Alert', 'Please select one of the shift types.');
    } else if (!this.stmrHeader.WELL_LOC?.trim()) {
      if (!noAlert) this.showAlert('Alert', 'Please provide a valid Location.');
    } else if (!this.stmrHeader.CHAIRED_BY?.trim()) {
      if (!noAlert) this.showAlert('Alert', 'Please provide a valid "Chaired By" name.');
    } else if (!this.stmrHeader.OPERATOR?.trim()) {
      if (!noAlert) this.showAlert('Alert', 'Please provide a valid Operator name.');
    } else if (!this.stmrCrew || this.stmrCrew.length === 0) {
      if (!noAlert) this.showAlert('Alert', 'Please add a minimum of one crew member.');
    } else {
      const activeCrew = this.stmrCrew.filter(c => c.P_MODE !== 'D');
      const nonThirdPartyCrew = activeCrew.filter(c => c.CREW_TYPE !== 'THIRD_PARTY');
      if (nonThirdPartyCrew.length === 0) {
        if (!noAlert) this.showAlert('Alert', 'Please add at least one non-third-party crew member.');
        keyMissing = true;
      } else {
        for (let crew of nonThirdPartyCrew) {
          if (!crew.CREW_SIGN || !crew.CREW_NAME?.trim()) {
            if (!noAlert) this.showAlert('Alert', 'Please capture names & signatures for all crew members.');
            keyMissing = true;
            break;
          }
        }
      }
      deleteCount = this.getNoOfDeletedCrew(this.stmrCrew);
      if (this.stmrCrew.length === deleteCount) {
        if (!noAlert) this.showAlert('Alert', 'Please add a minimum of one crew member.');
      } else if (!keyMissing) {
        if (!this.stmrTopicEntity || this.stmrTopicEntity.length === 0) {
          if (!noAlert) this.showAlert('Alert', 'Please add a minimum of one topic.');
        } else {
          deleteCount = this.getNoOfDeletedTopics(this.stmrTopicEntity);
          if (this.stmrTopicEntity.length === deleteCount) {
            if (!noAlert) this.showAlert('Alert', 'Please add a minimum of one topic.');
          } else if (isSubmit) {
            if (!this.stmrHeader.ONSITE_SUP?.trim()) {
              if (!noAlert) this.showAlert('Alert', 'Please provide a valid Crew Supervisor name.');
            } else if (!this.stmrHeader.ONSITE_SUP_SIGN) {
              if (!noAlert) this.showAlert('Alert', 'Please provide Crew Supervisor Signature.');
            } else {
              return true;
            }
          } else {
            return true;
          }
        }
      }
    }
    return false;
  }


  getNoOfDeletedCrew(crewList: any[]): number {
    return crewList.filter(c => c.P_MODE === 'D').length;
  }

  getNoOfDeletedTopics(topicList: any[]): number {
    return topicList.filter(t => t.topic?.P_MODE === 'D').length;
  }

  callbackAfterAddCrew(selectedCrew: CREW_HEADER[]) {
  this.ngZone.run(() => {
    this.stmrCrew = selectedCrew;   // store crew locally for display
    console.log('Selected Crew:', this.stmrCrew);
  });
}

  isSTMRReadonly(stmrHeader: STMR_HEADER): boolean {
  // STMR is readonly if it’s submitted and not in error state
  return (
    stmrHeader?.STMR_STATUS === 'SUBM' &&
    stmrHeader?.SYNC_STATUS !== AppConstants.SYNC_STATUS.ERROR
  );
}


  getSTMRIDForDisplay(formId: string): string {
    return formId?.startsWith('New') ? 'New' : formId;
  }


public returnDisplayDate(time: any, fromFormat?: string, toFormat?: string): string {
  return this.UtilityService.returnDisplayDate(time, fromFormat, toFormat);
}

  // In your component
onModelChange(value: 'DAY' | 'NIGHT'): void {
  console.log('[StmrDetailsPage] Shift changed:', value);

  this.stmrHeader.SHIFT = value;

  // Mark STMR as dirty (so Save/Save & Exit knows changes exist)
  (this.stmrHeader as any).saved = false;

  this.unviredSDK.logInfo(
    'StmrDetailsPage',
    'onModelChange',
    `Shift updated to ${value} for STMR ${this.stmrHeader?.STMR_ID}`
  );
}


  dispTime(date: string): string {
    if (!date) return '';
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';
    return d.toLocaleString();
  }
 // ✅ Crew callback
  callbackAfterCrew(crew: any[]) {
    this.unviredSDK.logInfo("STMRDetails", `callbackAfterCrew() crew members count: ${crew.length}`, "STMRDetailsPage");

    this.ngZone.run(() => {
      // Reset P_MODE for non-deleted items
      this.stmrCrew.forEach(member => {
        if (member.P_MODE !== 'D') {
          member.P_MODE = 'M';
        }
      });

      for (const c of crew) {
        let foundIndex = this.stmrCrew.findIndex(x => x.CREW_ID === c.PERSON_NO);

        if (foundIndex === -1 && c.isSelected) {
          // Insert new
          this.insertStmrCrewOnly(c);
        } else if (foundIndex !== -1) {
          if (c.isSelected) {
            // Update existing
            this.updateStmrCrewOnly(c);
          } else {
            // Delete existing
            this.deleteStmrCrewOnly(this.stmrCrew[foundIndex], foundIndex);
          }
        }
      }
    });
  }

  // ✅ Delete stmr crew
  deleteStmrCrewOnly(tempCrew: STMR_CREW, index: number) {
    if (this.stmrCrew[index].CREW_TYPE !== 'THIRD_PARTY') {
      this.stmrCrew[index].P_MODE = 'D';
      this.stmrCrew[index].OBJECT_STATUS = AppConstants.OBJECT_STATUS.DELETE;
      this.stmrCrew[index].CREW_SIGN = '';
      this.markSTMRAsUpdated();
    }
  }

  // ✅ Insert stmr crew
  insertStmrCrewOnly(tempCrew: any) {
    const itemStmrCrew = new STMR_CREW();
    itemStmrCrew.STMR_ID = this.stmrHeader.STMR_ID;
    itemStmrCrew.CREW_ID = tempCrew.PERSON_NO;
    itemStmrCrew.CREW_POS = tempCrew.DESIGNATION;
    itemStmrCrew.CREW_NAME = tempCrew.USER_NAME;
    itemStmrCrew.CREW_SIGN = tempCrew.CREW_SIGN;
    itemStmrCrew.FID = this.stmrHeader.LID;

    itemStmrCrew.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
    itemStmrCrew.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
    itemStmrCrew.P_MODE = 'A';

    this.updateSTMRHeaderLastSync(this.stmrHeader);
    this.stmrCrew.push(itemStmrCrew);
  }

  // ✅ Topic callback
  callbackAfterTopic(updatedStmrHeader: STMR_HEADER, topicEntity: STMR_TOPIC_ENTITY, stmrActions: STMR_ACTION[]) {
    this.stmrActions = [...this.stmrActions, ...stmrActions];
    this.stmrHeader = updatedStmrHeader;
    this.updateTopic(topicEntity);
    this.localSaveSTMR();
  }

  callbackAfterUserClosedAppInTopicsPage(updatedStmrHeader: STMR_HEADER, topicEntity: STMR_TOPIC_ENTITY, stmrActions: STMR_ACTION[]) {
    this.stmrActions = [...this.stmrActions, ...stmrActions];
    this.stmrHeader = updatedStmrHeader;
    this.updateTopic(topicEntity);
  }

  // ✅ Delete Topic
  async deleteStmrTopicOnly(tempTopic: STMR_TOPIC_ENTITY, index: number, fromTopicsScreen: boolean) {
    await this.confirmDeleteTopic(fromTopicsScreen, async () => {
      this.stmrTopicEntity[index].topic.P_MODE = "D";
      this.stmrTopicEntity[index].topic.OBJECT_STATUS = AppConstants.OBJECT_STATUS.DELETE;

      this.stmrTopicEntity[index].forms = this.stmrTopicEntity[index].forms.map((el: any) => ({
        ...el,
        P_MODE: 'D',
        OBJECT_STATUS: AppConstants.OBJECT_STATUS.DELETE
      }));

      this.stmrTopicEntity[index].data = this.stmrTopicEntity[index].data.map((el: any) => ({
        ...el,
        P_MODE: 'D',
        OBJECT_STATUS: AppConstants.OBJECT_STATUS.DELETE
      }));

      this.updateSTMRHeaderLastSync(this.stmrHeader);
    });
  }

  // ✅ Confirm delete
  private async confirmDeleteTopic(fromTopicsScreen: boolean, callback: () => void) {
    if (fromTopicsScreen) {
      callback();
    } else {
      const alert = await this.alertController.create({
        header: this.translate.instant("Delete Topic"),
        message: AppConstants.DELETE_FORMS_MSG,
        buttons: [
          {
            text: this.translate.instant('Delete'),
            handler: () => callback()
          },
          {
            text: this.translate.instant('Cancel'),
            role: 'cancel'
          }
        ]
      });
      await alert.present();
    }
  }

  // ✅ Update Topic
  updateTopic(topicEntity: STMR_TOPIC_ENTITY) {
    const topicIndex = this.stmrTopicEntity.findIndex(el => el.topic.TOPIC_NO === topicEntity.topic.TOPIC_NO);
    if (topicIndex >= 0) {
      this.stmrTopicEntity[topicIndex] = topicEntity;
    } else {
      this.stmrTopicEntity.push(topicEntity);
    }
    this.markSTMRAsUpdated();
  }

  // ✅ Capture crew sign
  async captureSign(signatureType: string, crew: any) {
    try {
      console.log('STMRDetails - captureSign called with:');
      console.log('  - signatureType:', signatureType);
      console.log('  - crew:', crew);
      console.log('  - crew.CREW_NAME:', crew?.CREW_NAME);
      console.log('  - crew.CREW_TYPE:', crew?.CREW_TYPE);

      if (!crew.CREW_NAME && crew.CREW_TYPE === "THIRD_PARTY") {
        console.log('STMRDetails - Missing crew name for third party');
        this.alertService.showAlert("Alert", "Please enter the name");
        return;
      }

      console.log('STMRDetails - Creating signature modal...');
      const modal = await this.modalController.create({
        component: SignaturePage,
        componentProps: {
          signatureType,
          crew,
          theme: this.styleTheme
        },
        backdropDismiss: false
      });

      console.log('STMRDetails - Modal created successfully');

      modal.onDidDismiss().then((res) => {
        try {
          console.log('STMRDetails - Modal dismissed with result:', res);
          const data = res.data;
          if (data?.signatureType && data?.crew) {
            console.log('STMRDetails - Processing signature data:', data.signatureType);
            switch (data.signatureType) {
              case 'ONSITE_SUP_SIGN':
                console.log('STMRDetails - Saving onsite supervisor signature');
                this.stmrHeader.ONSITE_SUP_SIGN = data.CREW_SIGN || "";
                this.stmrHeader.OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;
                this.updateSTMRHeaderLastSync(this.stmrHeader);
                break;

              case 'CREW_SIGN':
                console.log('STMRDetails - Saving crew signature for:', data.crew.CREW_ID);
                const crewIndex = this.stmrCrew.findIndex(c => c.CREW_ID === data.crew.CREW_ID);
                if (crewIndex >= 0) {
                  this.ngZone.run(() => {
                this.stmrCrew[crewIndex].CREW_SIGN = data.CREW_SIGN || "";
                this.stmrCrew[crewIndex].OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;
                this.updateSTMRHeaderLastSync(this.stmrHeader);
              });
            }
            break;
        }
      } else {
        console.log('STMRDetails - No signature data returned from modal');
      }
        } catch (dismissError) {
          console.error('STMRDetails - Error processing modal dismiss:', dismissError);
        }
      });

      console.log('STMRDetails - Presenting modal...');
      await modal.present();
      console.log('STMRDetails - Modal presented successfully');

    } catch (error) {
      console.error('STMRDetails - Error in captureSign:', error);
      if (error instanceof Error) {
        console.error('STMRDetails - Error stack:', error.stack);
        this.alertService.showAlert('Error', 'Failed to open signature capture: ' + error.message);
      } else {
        this.alertService.showAlert('Error', 'Failed to open signature capture: ' + String(error));
      }
    }
  }

  // ✅ Capture onsite sign
  async captureOnsiteSign(signatureType: string) {
    try {
      console.log('STMRDetails - captureOnsiteSign called with:', signatureType);
      console.log('STMRDetails - Current supervisor name:', this.stmrHeader.ONSITE_SUP);
      console.log('STMRDetails - Current supervisor signature exists:', !!this.stmrHeader.ONSITE_SUP_SIGN);

      let contCapture = false;
      let userName = "";
      let userSign = "";

      switch (signatureType) {
        case 'ONSITE_SUP_SIGN':
          if (this.stmrHeader.ONSITE_SUP && this.stmrHeader.ONSITE_SUP.trim()) {
            contCapture = true;
            userName = this.stmrHeader.ONSITE_SUP;
            userSign = this.stmrHeader.ONSITE_SUP_SIGN || "";
            console.log('STMRDetails - Supervisor signature capture approved for:', userName);
          } else {
            console.log('STMRDetails - Supervisor name missing, showing alert');
            this.alertService.showAlert("Error", "Enter Crew Supervisor Name");
            return;
          }
          break;
        default:
          console.error('STMRDetails - Unknown signature type:', signatureType);
          return;
      }

      if (contCapture) {
        const crew = new STMR_CREW();
        crew.CREW_NAME = userName;
        crew.CREW_SIGN = userSign;
        crew.CREW_POS = this.translate.instant("Crew Supervisor");

        console.log('STMRDetails - Calling captureSign for supervisor with crew object:', crew);
        await this.captureSign(signatureType, crew);
      }
    } catch (error) {
      console.error('STMRDetails - Error in captureOnsiteSign:', error);
      if (error instanceof Error) {
        this.alertService.showAlert('Error', 'Failed to capture supervisor signature: ' + error.message);
      } else {
        this.alertService.showAlert('Error', 'Failed to capture supervisor signature');
      }
    }
  }

  
  getNoOfNonThirdPartyCrewMembers(stmrCrew: any[]): number {
  if (!stmrCrew) return 0;

  return stmrCrew.filter(crew =>
    crew.CREW_TYPE !== 'THIRD_PARTY' &&
    crew.P_MODE !== 'D' &&
    crew.CREW_NAME && crew.CREW_NAME.trim().length > 0
  ).length;
}

  // ✅ Add a new 3rd party crew member
  addThirdParty() {
    this.ngZone.run(() => {
      let thirdPartyCrewHeader = this.getEmptyThirdPartyCrewHeader();
      this.stmrCrew.push(thirdPartyCrewHeader);
    });
  }

  // helper to create empty crew object
  getEmptyThirdPartyCrewHeader(): STMR_CREW {
    const crew = new STMR_CREW();
    crew.STMR_ID = this.stmrHeader.STMR_ID;
    crew.CREW_ID = this.UtilityService.guid32(); // Generate unique ID for third party crew
    crew.CREW_NAME = '';
    crew.CREW_POS = '';
    crew.CREW_SIGN = '';
    crew.CREW_TYPE = 'THIRD_PARTY';
    crew.P_MODE = 'A';
    crew.FID = this.stmrHeader.LID;
    crew.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
    crew.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
    return crew;
  }

  // ✅ Capture signature (modal-based)
  async captureSignOfThirdParty(signatureType: string, index: number) {
    let itemStmrCrew: STMR_CREW = new STMR_CREW();

    if (!this.stmrCrew[index].CREW_NAME) {
      this.showAlert('Alert', 'Please enter the name');
      return;
    }

    this.ngZone.run(() => {
      itemStmrCrew.CREW_NAME = this.stmrCrew[index].CREW_NAME.trim();
      this.captureSignForThirdParty(signatureType, itemStmrCrew, itemStmrCrew.CREW_NAME, index);
    });
  }

  async captureSignForThirdParty(signatureType: string, itemStmrCrew: STMR_CREW, UserName: string, index: number) {
    const modal = await this.modalController.create({
      component: SignaturePage,
      componentProps: {
        signatureType,
        crew: itemStmrCrew,
        theme: this.styleTheme,
      },
      backdropDismiss: false,
    });

    await modal.present();

    const { data } = await modal.onDidDismiss();

    if (data?.signatureType && data?.crew) {
      if (data.signatureType === 'CREW_SIGN') {
        this.ngZone.run(() => {
          this.stmrCrew[index].STMR_ID = this.stmrHeader.STMR_ID;
          this.stmrCrew[index].CREW_POS = '';
          this.stmrCrew[index].CREW_NAME = UserName;
          this.stmrCrew[index].CREW_SIGN = data.CREW_SIGN || '';
          this.stmrCrew[index].FID = this.stmrHeader.LID;
          this.stmrCrew[index].CREW_TYPE = 'THIRD_PARTY';
          this.stmrCrew[index].SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
          this.stmrCrew[index].OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
          this.stmrCrew[index].P_MODE = 'A';
          this.updateSTMRHeaderLastSync(this.stmrHeader);
        });
      }
    }
  }

  // ✅ Handle third party crew name changes
  onThirdPartyCrewNameChange(crew: STMR_CREW, index: number) {
    if (crew.CREW_NAME && crew.CREW_NAME.trim().length > 0) {
      // Update the crew member properties when name is entered
      crew.STMR_ID = this.stmrHeader.STMR_ID;
      crew.FID = this.stmrHeader.LID;
      crew.CREW_TYPE = 'THIRD_PARTY';
      crew.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
      crew.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
      crew.P_MODE = 'A';
      this.markSTMRAsUpdated();
    }
  }

  // ✅ Count helpers
  getNoOfThirdPartyCrewMembers(array: STMR_CREW[]) {
    return array.filter(c => c.P_MODE !== 'D' && c.CREW_TYPE === 'THIRD_PARTY').length;
  }

  // ✅ Remove crew member
  async removeCrewMember(crew: STMR_CREW) {
    const alert = await this.alertController.create({
      header: 'Delete Crew',
      message: AppConstants.DELETE_FORMS_MSG,
      buttons: [
        {
          text: 'Delete',
          handler: () => {
            this.ngZone.run(() => {
              let index = this.stmrCrew.indexOf(crew);
              this.stmrCrew[index].P_MODE = 'D';
              this.stmrCrew[index].OBJECT_STATUS = AppConstants.OBJECT_STATUS.DELETE;
              this.stmrCrew[index].CREW_SIGN = '';
              this.markSTMRAsUpdated();
            });
          },
        },
        { text: 'Cancel', role: 'cancel' },
      ],
    });

    await alert.present();
  }

  
    markSTMRAsPristine() {
    this.isSTMRUpdated = false;
    this.ngZone.run(() => {
      this.isSTMRComplete = this.validateSTMR(true, true);
    });
  }
  markSTMRAsUpdated() {
    this.isSTMRUpdated = true;
    this.ngZone.run(() => {
      this.isSTMRComplete = this.validateSTMR(true, true);
    });
  }
    async showAlert(header: string, message: string) {
    const a = await this.alertController.create({ header, message, buttons: ['OK'] });
    await a.present();
  }

  public updateSTMRHeaderLastSync(stmrHeader: STMR_HEADER): void {
  this.store.select(selectPrefilledData).subscribe(data => {
    if (!data) return; // Handle null/undefined if needed

    this.stmrHeader.LAST_SYNC_USER = data.USER_ID;
    this.stmrHeader.SUBM_BY = data.USER_ID;
    this.stmrHeader.TIME_ZONE = this.UtilityService.getTimezone();
    this.stmrHeader.OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;

    this.markSTMRAsUpdated();
  });
}


updateStmrCrewOnly(crew: any): void {
  this.ngZone.run(() => {
    for (let j = 0, jLen = this.stmrCrew.length; j < jLen; j++) {
      if (crew.PERSON_NO === this.stmrCrew[j].CREW_ID) {
        this.stmrCrew[j].CREW_NAME = crew.USER_NAME;
        this.stmrCrew[j].STMR_ID = this.stmrHeader.STMR_ID;
        this.stmrCrew[j].CREW_POS = crew.DESIGNATION;
        this.stmrCrew[j].CREW_SIGN = this.stmrCrew[j].CREW_SIGN;
        this.stmrCrew[j].FID = this.stmrHeader.LID;
        this.stmrCrew[j].SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
        this.stmrCrew[j].OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;
        this.stmrCrew[j].P_MODE = 'M';
      }
    }
    this.markSTMRAsUpdated();
  });
}
}