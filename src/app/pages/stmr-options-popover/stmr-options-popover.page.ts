import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppConstants } from 'src/app/constants/appConstants';
import { FormsModule } from '@angular/forms';
import { IonContent, IonList, IonItem, IonLabel,PopoverController } from '@ionic/angular/standalone';

@Component({
  selector: 'app-stmr-options-popover',
  templateUrl: './stmr-options-popover.page.html',
  styleUrls: ['./stmr-options-popover.page.scss'],
  standalone: true,
  imports: [IonContent, IonList, IonItem, IonLabel, CommonModule, FormsModule]
})
export class StmrOptionsPopoverPage {
  private popoverCtrl = inject(PopoverController);

  changeStyle() {
    this.popoverCtrl.dismiss({
      changeTheme: AppConstants.BOOL_TRUE
    });
  }

  printSTMR() {
    this.popoverCtrl.dismiss({
      print: AppConstants.BOOL_TRUE
    });
  }
}
