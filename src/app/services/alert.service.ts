import { Injectable } from '@angular/core';
import { <PERSON>ertController } from '@ionic/angular/standalone';
import { TranslateService } from '@ngx-translate/core';
@Injectable({
  providedIn: 'root'
})
export class AlertService {

  constructor(
  public alertController: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  public translate: TranslateService
) {}

public async showAlert(title: string, message: string): Promise<void> {
  const alert = await this.alertController.create({
    header: this.translate.instant(title) || title,
    subHeader: '',
    message: message,
    buttons: [this.translate.instant('OK')],
  });
  await alert.present();
}
}
