import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';
import { InAppBrowser, InAppBrowserObject } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { AlertService } from './alert.service';
import { CTA_DOC } from 'src/models/CTA_DOC';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';

import { unzipSync } from 'fflate';

@Injectable({
  providedIn: 'root'
})
export class DisplayCTAService {
  private myBrowser!: InAppBrowserObject;
  private selectedCTADoc: CTA_DOC = new CTA_DOC();
  private otherCTADocs: CTA_DOC[] = [];
  private rootfilePath = '';

  constructor(
    private file: File,
    private platform: Platform,
    private iab: InAppBrowser,
    private alertService: AlertService,
    private unviredSDK: UnviredCordovaSDK
  ) {}

  /**
   * Entrypoint: display the CTA document HTML by unzipping and opening index.html
   */
  async displayCTA(selectedCTA: CTA_DOC, otherCTADocs: CTA_DOC[], localFilePath: string) {
    this.selectedCTADoc = selectedCTA;
    this.otherCTADocs = otherCTADocs;
    this.rootfilePath = localFilePath;

    try {
      // 1. Get attachment for CTA
      const result = await this.unviredSDK.dbExecuteStatement(
        "SELECT * FROM TMPLT_ATTACHMENT WHERE TAG1 = 'CTA.CTA' AND TAG2 = 'CTA'"
      );

      if (result.type !== ResultType.success || !result.data.length) {
        return this.printError('Attachment not found.');
      }

      const attachment = result.data[0];

      // 2. Check if downloaded
      if (attachment.ATTACHMENT_STATUS !== 'DOWNLOADED' && attachment.ATTACHMENT_STATUS !== 2) {
        this.alertService.showAlert('Info', 'Downloading Attachment please wait...');
        return;
      }

      const zipFilePath = attachment.LOCAL_PATH;
      const pathResult = await this.unviredSDK.getAttachmentFolderPath();
      const attachmentPath = pathResult.data; // Root folder for extracted files

      // 3. Ensure clean folder & unzip
      await this.checkDirectory(attachmentPath, zipFilePath);

    } catch (error) {
      this.printError(error);
    }
  }

  /**
   * Remove folder if exists, then unzip
   */
  private async checkDirectory(attachmentPath: string, zipFilePath: string) {
    const dirName = `Display_CTA_${this.selectedCTADoc.CTA_ID}`;
    try {
      await this.file.checkDir(attachmentPath, dirName);
      await this.file.removeRecursively(attachmentPath, dirName);
    } catch {
      // Ignore folder-not-found errors
    }
    await this.unzipDirectory(attachmentPath, zipFilePath, dirName);
  }

  /**
   * Unzip using zip.js for both Electron and iOS
   */
  private async unzipDirectory(attachmentPath: string, zipFilePath: string, dirName: string) {
    try {
      let zipData: Uint8Array;

      if (this.platform.is('electron')) {
        const fs = (window as any).electronAPI.fs;
        zipData = new Uint8Array(fs.readFileSync(zipFilePath));

      } else if (this.platform.is('ios')) {
        const buffer = await this.file.readAsArrayBuffer(
          zipFilePath.substring(0, zipFilePath.lastIndexOf('/')),
          zipFilePath.substring(zipFilePath.lastIndexOf('/') + 1)
        );
        zipData = new Uint8Array(buffer);

      } else {
        throw new Error('Unsupported platform for unzip');
      }

      // ---- Unzip using fflate ----
      const files = unzipSync(zipData);

      for (const [filename, content] of Object.entries(files)) {
        const outDirFull = `${attachmentPath}/${dirName}`;

        if (this.platform.is('electron')) {
          const fs = (window as any).electronAPI.fs;
          const path = (window as any).electronAPI.path;

          const folder = path.dirname(`${outDirFull}/${filename}`);
          if (!fs.existsSync(folder)) {
            fs.mkdirSync(folder, { recursive: true });
          }
          fs.writeFileSync(`${outDirFull}/${filename}`, content);

        } else if (this.platform.is('ios')) {
          await this.file.createDir(attachmentPath, dirName, true);
          const arrayBuffer: ArrayBuffer = new Uint8Array(content as Uint8Array).buffer;
          await this.file.writeFile(outDirFull, filename, arrayBuffer, { replace: true });
        }
      }

      await this.updateFormData(attachmentPath, dirName);

    } catch (error: any) {
      this.printError(`Unzip error (fflate): ${error?.message || error}`);
    }
  }


  /**
   * Reads index.html, injects formData + logo JS, writes back
   */
  private async updateFormData(attachmentPath: string, dirName: string) {
    try {
      const folderPath = `${attachmentPath}/${dirName}`;
      const readTextContent = await this.file.readAsText(folderPath, 'index.html');

      // Fetch company logo
      const logoResult = await this.unviredSDK.dbExecuteStatement(
        `SELECT C.* FROM (SELECT COMP_CODE FROM RIG_HEADER) AS R 
         LEFT JOIN (SELECT * FROM COMPANY_HEADER) AS C 
         ON R.COMP_CODE = C.CODE`
      );

      let logo = '';
      if (logoResult.type === ResultType.success && logoResult.data.length > 0) {
        logo = logoResult.data[0].LOGO || '';
      }

      const script = `var formData = ${this.getFormData()}; 
                       var companyLogo = '${logo}'`;

      const fileContents = readTextContent.replace(
        '<!-- FORM DATA -->',
        `<!-- FORM DATA -->\n<script>\n${script}\n</script>\n<!-- ./FORM DATA -->`
      );

      await this.file.writeExistingFile(folderPath, 'index.html', fileContents);

      this.openFileInBrowser(`${folderPath}/index.html`);
    } catch (error) {
      this.printError(error);
    }
  }

  /**
   * Creates the JS object string for formData injection
   */
  private getFormData(): string {
    const formData: any = {};
    if (!this.rootfilePath.endsWith('\\')) {
      this.rootfilePath += '\\';
    }
    formData.path = this.rootfilePath.replace(/\\\\/g, '\\');
    formData.document = this.selectedCTADoc.FILE_NAME.replace(/\\\\/g, '\\');

    formData.docs = this.otherCTADocs.map(doc => ({
      filename: doc.FILE_NAME.replace(/\\\\/g, '\\')
    }));

    return JSON.stringify(formData, null, 2);
  }

  /**
   * Opens the HTML file on platform
   */
  private openFileInBrowser(filePath: string) {
    if (this.platform.is('electron')) {
      const { shell } = (window as any).require('electron');
      shell.openPath(filePath);
    } else if (this.platform.is('ios')) {
      const urlEncodedPath = encodeURI(filePath);
      this.viewFileInAppBrowser(urlEncodedPath);
    }
  }

  private viewFileInAppBrowser(urlEncodedPath: string) {
    this.myBrowser = this.iab.create(urlEncodedPath, '_blank', {
      location: 'no',
      clearcache: 'yes',
      clearsessioncache: 'yes',
      fullscreen: 'yes'
    });
    this.myBrowser.on('loadstop').subscribe(() => {
      this.myBrowser.show();
    });
  }

  private printError(errMsg: any) {
    this.alertService.showAlert(
      'Display CTA Error',
      typeof errMsg === 'string' ? errMsg : JSON.stringify(errMsg, null, 2)
    );
  }
}
