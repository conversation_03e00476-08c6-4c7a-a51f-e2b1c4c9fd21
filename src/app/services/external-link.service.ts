import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';

@Injectable({
  providedIn: 'root'
})
export class ExternalLinkService {

  constructor(private platform: Platform) {}

  open(url: string): void {
    if (window?.electronAPI?.openExternal) {
      //Electron (via preload.js contextBridge)
      window.electronAPI.openExternal(url);
    } else if (this.platform.is('ios')) {
      // iOS 
      window.open(url, '_system');
    } else {
      // Fallback for web 
      window.open(url, '_blank');
    }
  }
}
