import { Injectable } from '@angular/core';
import { AlertService } from './alert.service'; // Renamed to PascalCase as per TS style guide
import { AppConstants } from '../constants/appConstants';

@Injectable({
  providedIn: 'root'
})
export class FormUtilityService {

  constructor(
    private alertService: AlertService
  ) {}

  /**
   * Extracts JSON data from a loaded form HTML inside an InAppBrowser-like instance
   * @param myBrowser Reference to InAppBrowser or WebView
   * @param callback Function to call with the JSON data
   */
  public getFormDataFromHTML(
    myBrowser: any,
    callback: (data: any[]) => void
  ): void {
    this.generateJSON(myBrowser)
      .then(result => {
        if (result && result.length > 0) {
          callback(result);
        } else {
          myBrowser?.close?.();
          this.displayNoDataErr();
        }
      })
      .catch(error => {
        myBrowser?.close?.();
        this.displayNoDataErr(error);
      });
  }

  /**
   * Executes the `generateJSON()` function inside the browser/webview
   */
  private async generateJSON(myBrowser: any): Promise<any[]> {
    try {
      const result = await myBrowser.executeScript({
        code: 'generateJSON()'
      });

      if (Array.isArray(result) && result.length > 0) {
        return result;
      }
      return [];
    } catch (err) {
      console.error('Error executing generateJSON():', err);
      return [];
    }
  }

  /**
   * Displays an error alert when data reading fails
   */
  private displayNoDataErr(error?: string): void {
    let errMsg = AppConstants.READING_FORM_DATA_ERR_MSG;
    if (error) {
      errMsg += `<br/>${error}`;
    }
    this.alertService.showAlert('Error reading data!', errMsg);
  }
}
