import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';
import { ExternalLinkService } from './external-link.service';
import { AlertService } from './alert.service';
import { File } from '@awesome-cordova-plugins/file/ngx'
import * as moment from 'moment-timezone';
import { unzipSync } from 'fflate';
import { UnviredCordovaSDK , ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
declare var Windows: any;
declare var cordova: any;
@Injectable({
  providedIn: 'root',
})
export class PrintSTMRFormService {

  private stmrId: string = '';
  private stmrObj: any;

  constructor(
    private file: File,
    private platform: Platform,
    private externalLink: ExternalLinkService,
    private alertService: AlertService,
    private unviredSdk: UnviredCordovaSDK,
  ) {}

  private formatDateTime(timestamp: any, timezone: string): string {
    if (!timestamp || !timezone) return timestamp;
    const ts = Number(timestamp);
    if (ts === 0) return timestamp;
    return moment.unix(ts).tz(timezone).format('MMM DD YYYY HH:mm z') + ' (24Hrs)';
  }

  private formatStmrObject(stmrObj: any): any {
    const tz = stmrObj.stmrHeader?.TIME_ZONE;
    if (!tz) return stmrObj;

    const header = stmrObj.stmrHeader;
    header.CRTD_ON_FORMATTED = this.formatDateTime(header.CRTD_ON, tz);
    header.SHIFT_TIME_FORMATTED = this.formatDateTime(header.SHIFT_TIME, tz);
    header.LAST_SYNC_TIME_FORMATTED = this.formatDateTime(header.LAST_SYNC_TIME, tz);
    header.DATE_COMP_FORMATTED = this.formatDateTime(header.DATE_COMP, tz);

    stmrObj.stmrTopic = (stmrObj.stmrTopic || []).map((topic: any) => ({
      ...topic,
      TOPIC_START_FORMATTED: this.formatDateTime(topic.TOPIC_START, tz)
    }));

    return stmrObj;
  }

 public async printSTMRForm(stmrId: string, stmrObj: any): Promise<void> {
  this.stmrId = stmrId || stmrObj?.stmrHeader?.STMR_ID;
  this.stmrObj = this.formatStmrObject(stmrObj);

  const isElectron = this.platform.is('electron') 
  const isIOS = this.platform.is('ios');

  if (!isElectron && !isIOS) {
    this.printError('Printing is only supported on Electron (Windows) and iOS.');
    return;
  }

  try {
    // 1. Fetch attachment row
    const result = await this.unviredSdk.dbExecuteStatement(
      "SELECT * FROM TMPLT_ATTACHMENT WHERE TAG1 = 'STMR.STMR' AND TAG2 = 'STMR'"
    );

    if (result.type !== ResultType.success || !result.data.length) {
      this.printError('Attachment not found.');
      return;
    }

    const attachment = result.data[0];

    if (attachment.ATTACHMENT_STATUS !== 'DOWNLOADED' && attachment.ATTACHMENT_STATUS !== 2) {
      this.alertService.showAlert('Info', 'Downloading Attachment, please wait...');
      return;
    }

    // 2. Construct paths
    const zipFilePath = attachment.LOCAL_PATH; // Full path to ZIP file
    const folderName = `Print_STMR_${this.stmrId}`;
    const srcPath = zipFilePath.replace(/\\[^\\]+$/, `\\${folderName}`);

    const basePath = isElectron
      ? (window.userDataPath || '') + '/Attachments'  // Electron: userDataPath exposed from preload
      : await this.getAttachmentFolderPath();         // iOS: from SDK

    // 3. Proceed to unzip and update HTML
    this.prepareAndOpenPrintForm(basePath, zipFilePath, srcPath, folderName);

  } catch (error: any) {
    this.printError(error.message || error);
  }
}


 private async getAttachmentFolderPath(): Promise<string> {
  try {
    const result = await this.unviredSdk.getAttachmentFolderPath();
    if (result?.data) {
      return result.data;
    } else {
      throw new Error('Attachment path not found.');
    }
  } catch (error : any) {
    throw new Error('Failed to get attachment path: ' + (error?.message || error));
  }
}


  private async prepareAndOpenPrintForm(basePath: string, zipFilePath: string, srcPath: string, folderName: string) {
    try {
      await this.file.checkDir(basePath, folderName);
      await this.file.removeRecursively(basePath, folderName);
    } catch (_) {
      // Directory doesn't exist - safe to ignore
    }

    // Unzip archive
    await this.unzipAndUpdateHtml(basePath, zipFilePath, folderName);
  }



private async unzipAndUpdateHtml(
  basePath: string,
  zipFilePath: string,
  folderName: string
): Promise<void> {
  const outputDir = `${basePath}/${folderName}`;

  try {
    let zipData: Uint8Array;

    // ----- Electron -----
    if (this.platform.is('electron')) {
      const fs = (window as any).electronAPI.fs;
      zipData = new Uint8Array(fs.readFileSync(zipFilePath));

    // ----- Cordova iOS -----
    } else if (this.platform.is('ios')) {
      const buffer = await this.file.readAsArrayBuffer(basePath, `${folderName}.zip`);
      zipData = new Uint8Array(buffer);

    } else {
      throw new Error('Platform not supported for unzipping.');
    }

    // ----- Unzip -----
    const files = unzipSync(zipData); // { filename: Uint8Array }

    // ----- Write extracted files -----
    for (const [filename, content] of Object.entries(files)) {
      if (this.platform.is('electron')) {
        const fs = (window as any).electronAPI.fs;
        const path = (window as any).electronAPI.path;

        const outFilePath = path.join(outputDir, filename);
        fs.mkdirSync(path.dirname(outFilePath), { recursive: true });
        fs.writeFileSync(outFilePath, content);

      } else if (this.platform.is('ios')) {
        await this.file.createDir(basePath, folderName, true);
         const arrayBuffer: ArrayBuffer = new Uint8Array(content as Uint8Array).buffer;
        await this.file.writeFile(`${outputDir}/`, filename, arrayBuffer, { replace: true });
      }
    }

    // ----- Update HTML after unzip -----
    await this.updateHtmlAndOpen(basePath, folderName);

  } catch (error: any) {
    this.printError('Unzip error (fflate): ' + (error?.message || error));
  }
}


  private async updateHtmlAndOpen(basePath: string, folderName: string): Promise<void> {
  const htmlFile = 'index.html';
  const folderPath = `${basePath}/${folderName}`;

  try {
    // 1. Read the existing HTML file
    const content = await this.file.readAsText(folderPath, htmlFile);

    // 2. Fetch company logo using unviredSdk.dbExecuteStatement
    const query = `
      SELECT C.* FROM 
        (SELECT COMP_CODE FROM RIG_HEADER) AS R 
        LEFT JOIN 
        (SELECT * FROM COMPANY_HEADER) AS C 
        ON R.COMP_CODE = C.CODE
    `;

    const result = await this.unviredSdk.dbExecuteStatement(query);

    if (result.type !== ResultType.success || !result.data?.length) {
      this.printError('Company logo not found.');
      return;
    }

    const logo = result.data[0].LOGO || '';

    // 3. Build the <script> block with form data
    const formScript = `
      var formData = ${JSON.stringify(this.stmrObj)};
      var companyLogo = '${logo}';
      var shouldPrint = true;
    `;

    // 4. Inject form data into HTML
    const updatedHtml = content.replace(
      '<!-- FORM DATA -->',
      `<!-- FORM DATA -->\n<script>${formScript}</script>\n<!-- ./FORM DATA -->`
    );

    // 5. Write updated HTML back to file
    await this.file.writeExistingFile(folderPath, htmlFile, updatedHtml);

    // 6. Resolve path and open in external browser (Electron or iOS)
    const finalPath = this.resolvePlatformFilePath(folderName);
    this.externalLink.open(finalPath);

  } catch (err: any) {
    this.printError('Failed to update HTML: ' + (err?.message || JSON.stringify(err)));
  }
}


  private resolvePlatformFilePath(folderName: string): string {
  const fileName = 'index.html';

  if (this.platform.is('electron')) {
    // Electron/Windows: use Windows API if available, else use userDataPath
    const windowsPath = (window as any)?.Windows?.Storage?.ApplicationData?.current?.localFolder?.path;
    if (windowsPath) {
      return `${windowsPath}\\Attachments\\${folderName}\\${fileName}`;
    }
    return `${window.userDataPath}/Attachments/${folderName}/${fileName}`;
  }

 if (this.platform.is('ios')) {
  const basePath = window.userDataPath || cordova.file.documentsDirectory;
  return `${basePath}Attachments/${folderName}/${fileName}`;
}
  // Unsupported platform
  this.printError('Unsupported platform for resolving file path.');
  return '';
}


  private printError(message: string): void {
    this.alertService.showAlert('Print Failed', message);
  }
}