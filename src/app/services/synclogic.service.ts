import { Injectable, NgZone } from '@angular/core';
import { AlertController, Platform } from '@ionic/angular';
import { AttachmentHelper, getBasePath, isElectron, normalizeLocalPath } from '../helpers/attachment-helper';
import { UnviredCordovaSDK, RequestType, ResultType, LogLevel } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { DataService } from './data.service';
import { HttpClient } from '@angular/common/http';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, firstValueFrom, filter, take, skip } from 'rxjs';
import { AppConstants } from '../constants/appConstants';
import { Store } from '@ngrx/store';
import * as SyncActions from '../store/store.actions';
import { selectGraphToken, selectSiteHeaders, selectSiteMeta } from '../store/store.selector';

@Injectable({
  providedIn: 'root'
})
export class SynclogicService {
  public isSyncing$ = new BehaviorSubject<boolean>(false);
  public progress$ = new BehaviorSubject<number>(0);
  public currentSyncFile$ = new BehaviorSubject<string>('');
  private currentGraphApiToken: string | null = null;
  private networkError = false;
  private isSiteHeaderReady = false;
  private pendingSync = false;
  private syncInProgressFlag = false;

  constructor(
    private attachmentHelper: AttachmentHelper,
    private unviredSDK: UnviredCordovaSDK,
    private dataService: DataService,
    private http: HttpClient,
    private alertController: AlertController,
    private platform: Platform,
    private ngZone: NgZone,
    private translate: TranslateService,
    private store: Store
  ) {
    this.isSyncing$.next(false);
    this.store.select(selectSiteMeta).subscribe(() => {}); 
  }

  /**
 * Normalize a path but preserve the file:// or file:/// protocol prefix.
 * - Keeps the file://( / / ) prefix intact.
 * - Collapses duplicate slashes only in the path portion.
 */
  private sanitizePathPreserveFileScheme(path: string): string {
    if (!path || typeof path !== 'string') return path;

    const fileSchemeMatch = path.match(/^(file:\/\/\/|file:\/\/)/i);
    let prefix = '';
    let rest = path;

    if (fileSchemeMatch) {
      prefix = fileSchemeMatch[0];       
      rest = path.slice(prefix.length); 
    }
    rest = rest.replace(/\/{2,}/g, '/');

    // Rebuild and return
    return prefix + rest;
  }

  /**
 * Main entry point for "Rig Docs" sync.
 * Fetches Graph API token, site headers, document metadata, and downloads required files.
 * @param triggeredByUser - whether this sync was manually triggered by the user
 */

  async onRigDocSync(triggeredByUser = false) {
    console.log('[SynclogicService] onRigDocSync called');
    // Set log level for the SDK
    try {
      this.unviredSDK.setLogLevel(LogLevel.debug);
    } catch (error) {
      console.error('Failed to set log level:', error);
    }

    this.unviredSDK.logInfo("SynclogicService", "onRigDocSync", "Sync process started.");

    // 1️ Check for internet connectivity before starting
    const isOnline = await this.unviredSDK.hasInternet();
    if (!isOnline) {
      try {
        this.unviredSDK.logError("SynclogicService", "onRigDocSync", "No network - exiting sync early.");
      } catch { }
      const alert = await this.alertController.create({
        header: this.translate.instant("No Network"),
        message: this.translate.instant("You are not connected to the network. Please connect to the internet and try again."),
        buttons: ['OK']
      });
      await alert.present();
      return;
    }
    // 2️ Prevent duplicate sync runs
    if (this.isSyncing$.value) {
      if (triggeredByUser) {
        const alert = await this.alertController.create({
          header: this.translate.instant('Sync In Progress'),
          message: this.translate.instant('A sync is already running. Please wait until it finishes.'),
          buttons: ['OK']
        });
        await alert.present();
      }
      try {
        this.unviredSDK.logInfo("SynclogicService", "onRigDocSync", "Sync already in progress - skipping new request.");
      } catch { } return;
    }
    // 3️ Initialize sync state
    this.ngZone.run(() => {
      this.isSyncing$.next(true);
      this.progress$.next(0);
      this.currentSyncFile$.next('');
    });
    this.store.dispatch(SyncActions.rigDocSyncStart({ triggeredByUser }));
    console.log('[SynclogicService] SYNC START dispatched');
    this.store.dispatch(SyncActions.syncSetIsSyncing({ isSyncing: true }));
    this.networkError = false;
    try {
      // 4️ Fetch Microsoft Graph API token
      try {
        this.unviredSDK.logInfo("SynclogicService", "onRigDocSync", "Obtaining Graph API token.");
      } catch { }
      this.store.dispatch(SyncActions.loadGraphToken());
      const accessToken = await firstValueFrom(
        this.store.select(selectGraphToken).pipe(filter((t: any) => !!t), take(1))
      );
      if (!accessToken) {
        try {
          this.unviredSDK.logError("SynclogicService", "onRigDocSync", "Could not obtain Graph API token.");
        } catch { } alert('Could not retrieve Graph API token');
        return;
      }
      this.currentGraphApiToken = accessToken;

      // 5️ Get list of SharePoint sites (site headers) from local DB
      // This is where we get the **sitepath** (SP_SITE_NAME) which is used later to query drives
      this.store.dispatch(SyncActions.loadSharepointSites());
      const siteHeaderList = await firstValueFrom(
        this.store.select(selectSiteHeaders).pipe(filter((arr: any[]) => Array.isArray(arr) && arr.length > 0), take(1))
      );

      try {
        this.unviredSDK.logInfo("SynclogicService", "onRigDocSync", "Fetching document list from server.");
      } catch { }

      // 6️ Fetch document metadata (list of files/folders to sync) from backend via store
      this.store.dispatch(SyncActions.loadSiteMeta());
      const siteMetaArray = await firstValueFrom(
        this.store.select(selectSiteMeta).pipe(
          skip(1),
          filter((arr: any[]) => Array.isArray(arr)),
          take(1)
        )
      );

      let allDocs: any[] = [];
      // 7️ Merge site headers (for sitepath) with document metadata
      for (const siteBlock of siteMetaArray) {
        const siteMetaHeader = siteBlock.SP_SITE_META_HEADER;

        // Find the matching site header from DB using SP_SITE_ID
        const siteHeader = siteHeaderList.find((sh: any) => sh.SP_SITE_ID === siteMetaHeader.SP_SITE_ID);
        if (!siteHeader) {
          try {
            this.unviredSDK.logError(
              "SynclogicService", "onRigDocSync",
              `No site header found for SP_SITE_ID: ${siteMetaHeader.SP_SITE_ID}`
            );
          } catch { }
          console.warn(`No site header found for SP_SITE_ID: ${siteMetaHeader.SP_SITE_ID}`);
          continue;
        }

        //  The `sitepath` is taken from SP_SITE_NAME (site display name or path from DB)
        const sitepath = siteHeader.SP_SITE_NAME;
        const library = siteMetaHeader?.DOC_LIBRARY;

        // Map each document with its site/library/path info
        for (const doc of (siteBlock.SP_SITE_DOC || [])) {
          const mappedDoc = {
            ...doc,
            SP_SITE_ID: siteMetaHeader.SP_SITE_ID,
            id: doc.DOC_ID,
            name: doc.DOC_NAME,
            localPath: doc.LOCAL_PATH,
            sitepath,
            library,
            isFolder: doc.IS_FOLDER === 'true' || doc.IS_FOLDER === true,
            syncAttempted: false,
            syncSuccess: false,
            syncError: ''
          };
          allDocs.push(mappedDoc);
        }
      }

      //  At this point, every document now has:sitepath (from getSharepointSites)
      // - library (from metadata)localPath (from backend)
      // These are essential for later download steps.

      // 8️ Filter valid documents
      const activeDocs = allDocs.filter(doc =>
        doc.id && doc.name && doc.localPath && doc.sitepath && doc.library
      );
      try {
        this.unviredSDK.logInfo(
          "SynclogicService", "onRigDocSync",
          `Ready to process ${activeDocs.length} documents for download.`
        );
      } catch { }
      if (activeDocs.length === 0) {
        try {
          this.unviredSDK.logError("SynclogicService", "onRigDocSync", "No valid documents to sync.");
        } catch { }
        alert('No valid documents to sync.');
        return;
      }

      const totalFiles = activeDocs.length;
      let processedFiles = 0;
      const driveIdCache: { [key: string]: string } = {};

      // 9️ Proceed to download each document
      const downloadWorker = async (doc: any) => {
        let basePath = '';
        if (isElectron()) {
          try {
            const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_SP_CONFIG_HEADER, {});
            if (result.type === ResultType.success && result.data.length > 0) {
              basePath = result.data[0].LOCAL_PATH || '';
              basePath = basePath.replace(/\\/g, '/').replace(/\/+$/, ''); // normalize trailing slashes
            } else {
              console.warn('SP_CONFIG_HEADER table empty! Falling back to blank basePath.');
              basePath = '';
            }
          } catch (err) {
            console.error('Failed to fetch SP_CONFIG_HEADER.LOCAL_PATH for Electron:', err);
          }
        } else {
          basePath = getBasePath();
        }

        // Normalize the local path for the current platform
        const normalizedLocalPath = normalizeLocalPath(doc.localPath);
        let relativePath = normalizedLocalPath;
        relativePath = relativePath.replace(/^\/+/, '');
        const fullPath: string = basePath ? `${basePath}${relativePath}` : relativePath; // Don't add extra slash!

        const sanitizedFullPath = this.sanitizePathPreserveFileScheme(fullPath);


        const libraryName = doc.library.replace(/\\/g, '/').replace(/^\/+|\/+$/g, '');

        try {
          this.unviredSDK.logInfo(
            "SynclogicService", "downloadWorker",
            `Cycle start: Preparing to download: ${doc.name}, Original Path: ${doc.localPath}, Normalized Path: ${normalizedLocalPath}, Full Path: ${sanitizedFullPath}`
          );
        } catch { }

        if (!normalizedLocalPath || !libraryName) {
          try {
            this.unviredSDK.logError("SynclogicService", "downloadWorker", `Missing localPath or library in doc: ${doc.name}`);
          } catch { }
          console.warn('Missing localPath or library in doc:', doc);
          return;
        }

        if (doc.isFolder) {
          try {
            await this.attachmentHelper.createDirectoriesRecursively(sanitizedFullPath);
            this.unviredSDK.logInfo(
              "SynclogicService", "downloadWorker",
              `Directory created for folder: ${doc.name}`
            );
            return;
          } catch (err) {
            try {
              this.unviredSDK.logError(
                "SynclogicService", "downloadWorker",
                `Failed to create folder: ${sanitizedFullPath}, Error: ${err}`
              );
            } catch { }
            console.error('Failed to create folder:', sanitizedFullPath, err);
            return;
          }
        }

        const folderPath = sanitizedFullPath.substring(0, sanitizedFullPath.lastIndexOf('/'));
        await this.attachmentHelper.createDirectoriesRecursively(folderPath);

        try {
          await this.attachmentHelper.readExternalFile(sanitizedFullPath);
          this.unviredSDK.logInfo(
            "SynclogicService", "downloadWorker",
            `File already exists locally: ${sanitizedFullPath}`
          );
          return;
        } catch {
          // File doesn't exist — proceed
        }

        const siteKey = `${doc.sitepath}_${doc.library}`;
        let driveId = driveIdCache[siteKey];

        if (!driveId) {
          const sitePath = encodeURIComponent(doc.sitepath);
          const drivesResponse: any = await this.graphApiGetWithTokenRetry(
            `https://graph.microsoft.com/v1.0/sites/${sitePath}/drives`
          );
          const drive = drivesResponse.value.find((d: any) => d.name === doc.library);
          if (!drive) {
            try {
              this.unviredSDK.logError(
                "SynclogicService", "downloadWorker",
                `Drive not found for library: ${doc.library}`
              );
            } catch { }
            console.error('Drive not found for library:', doc.library);
            return;
          }
          driveId = drive.id;
          driveIdCache[siteKey] = driveId;
        }

        const itemId = doc.id;
        const downloadUrl = `https://graph.microsoft.com/v1.0/drives/${driveId}/items/${itemId}/content`;

        try {
          this.unviredSDK.logInfo(
            "SynclogicService", "downloadWorker",
            `Initiating download: ${downloadUrl}`
          );
          const response = await this.graphApiGetWithTokenRetry(
            downloadUrl,
            { responseType: 'blob' as 'blob' }
          );
          let arrayBuffer;
          let httpStatus = 200;
          if (response && response.body && typeof response.status === "number") {
            arrayBuffer = await new Response(response.body).arrayBuffer();
            httpStatus = response.status;
          } else {
            arrayBuffer = await new Response(response).arrayBuffer();
          }
          try {
            this.unviredSDK.logInfo(
              "SynclogicService", "downloadWorker",
              `Received HTTP status ${httpStatus} for file: ${doc.name}, ID: ${doc.id}`
            );
          } catch { }
          this.attachmentHelper.writeExternalFile(sanitizedFullPath, arrayBuffer);
          doc.downloaded = true;
          try {
            this.unviredSDK.logInfo(
              "SynclogicService", "downloadWorker",
              `Downloaded and saved successfully: ${doc.name}, Path: ${sanitizedFullPath}`
            );
          } catch { }
        } catch (err: any) {
          doc.downloaded = false;
          const statusCode = err.status ? err.status : (err.error && err.error.status) ? err.error.status : 'unknown';
          try {
            this.unviredSDK.logError(
              "SynclogicService", "downloadWorker",
              `Download failed for: ${downloadUrl}, HTTP status: ${statusCode}, Error: ${err.message || err}`
            );
          } catch { }
          console.error(`Download failed for: ${downloadUrl}`, err);
          if (statusCode === 404) {
            try {
              this.unviredSDK.logError(
                "SynclogicService", "downloadWorker",
                `File not found (404): ${doc.name}`
              );
            } catch { };
            console.error(' File not found (404):', doc.name);
          } else if (statusCode === 403) {
            try {
              this.unviredSDK.logError(
                "SynclogicService", "downloadWorker",
                `Permission denied (403): ${doc.name}`
              );
            } catch { }
            console.error(' Permission denied:', doc.name);
          } else {
            console.error('Unhandled error for:', doc.name, err);
            return;
          }
        }
      };

      const MAX_CONCURRENT_DOWNLOADS = 5;
      let activeDownloads = 0;
      let downloadIndex = 0;

      const startNextDownload = async () => {
        if (downloadIndex >= activeDocs.length || this.networkError) return;

        const doc = activeDocs[downloadIndex++];
        activeDownloads++;

        await downloadWorker(doc);

        activeDownloads--;

        // Update progress after each file
        this.ngZone.run(() => {
          processedFiles++;
          const currentFile = doc.localPath?.split(/[\\/]/)[2] || doc.name;
          const progress = totalFiles > 0 ? processedFiles / totalFiles : 0;
          this.currentSyncFile$.next(currentFile);
          this.progress$.next(progress);
          this.store.dispatch(SyncActions.syncSetCurrentFile({ currentSyncFile: currentFile }));
          this.store.dispatch(SyncActions.syncSetProgress({ progress }));
        });

        await startNextDownload(); // start next download when one finishes
      };

      const downloadPromises: Promise<void>[] = [];
      for (let i = 0; i < MAX_CONCURRENT_DOWNLOADS; i++) {
        downloadPromises.push(startNextDownload());
      }

      await Promise.all(downloadPromises);
      try {
        this.unviredSDK.logInfo(
          "SynclogicService", "onRigDocSync",
          `Sync completed. Successfully processed ${processedFiles} out of ${totalFiles} files.`
        );
      } catch { }
      const deletedDocs = allDocs.filter(doc => doc.MARKED_FOR_DELETION === true);
      for (const doc of deletedDocs) {
        try {
          const normalizedPath = normalizeLocalPath(doc.localPath);
          await this.attachmentHelper.deleteExternalFile(normalizedPath);
          this.unviredSDK.logInfo(
            "SynclogicService", "onRigDocSync",
            `Deleted file: ${normalizedPath}`
          );
          console.log(`Deleted file: ${normalizedPath}`);
        } catch (err) {
          try {
            this.unviredSDK.logError(
              "SynclogicService", "onRigDocSync",
              `Failed to delete ${doc.localPath}, Error: ${err}`
            );
          } catch { }
          console.error(`Failed to delete ${doc.localPath}`, err);
        }
      }

      if (!this.networkError) {
        // Update and verify status via store
        this.store.dispatch(SyncActions.updateDeviceSiteMeta({ documents: allDocs }));
        this.store.dispatch(SyncActions.checkServerSiteMeta());
        await this.generateFolderStructureJsons(allDocs);
      }

    } catch (error: any) {
      try {
        this.unviredSDK.logError(
          "SynclogicService", "onRigDocSync",
          "Error during Rig Doc Sync: " + error
        );
      } catch { }
      console.error('Error during Rig Doc Sync:', error);

      // Show user-friendly error message
      if (error.message && error.message.includes('cdv-plugin-exec')) {
        const alert = await this.alertController.create({
          header: this.translate.instant('Sync Error'),
          message: this.translate.instant('There was an issue with file system access. Please try again or contact support.'),
          buttons: ['OK']
        });
        await alert.present();
      }
    } finally {
      this.isSyncing$.next(false);
      this.progress$.next(0);
      this.currentSyncFile$.next('');
      this.store.dispatch(SyncActions.syncSetIsSyncing({ isSyncing: false }));
      this.store.dispatch(SyncActions.syncSetProgress({ progress: 0 }));
      this.store.dispatch(SyncActions.syncSetCurrentFile({ currentSyncFile: '' }));
      this.store.dispatch(SyncActions.rigDocSyncEnd());
      try {
        this.unviredSDK.logInfo("SynclogicService", "onRigDocSync", "Sync process ended.");
      } catch { }
    }
  }

  async graphApiGetWithTokenRetry(url: string, options: any = {}): Promise<any> {
    let triedRefresh = false;
    while (true) {
      try {
        const headers = { Authorization: `Bearer ${this.currentGraphApiToken}` };
        return await this.http.get(url, { ...options, headers }).toPromise();
      } catch (err: any) {
        if (err?.status === 401 && !triedRefresh) {
          // Refresh the token via store effect
          this.store.dispatch(SyncActions.loadGraphToken());
          this.currentGraphApiToken = await firstValueFrom(
            this.store.select(selectGraphToken).pipe(filter((t: any) => !!t), take(1))
          );
          if (!this.currentGraphApiToken) throw new Error('Could not refresh Graph API token after 401');
          triedRefresh = true;
          continue;
        }
        throw err;
      }
    }
  }


  async generateFolderStructureJsons(allDocs: any[]) {
    const folderGroups: { [folderName: string]: any[] } = {};

    for (const doc of allDocs) {
      const normalizedPath = normalizeLocalPath(doc.localPath);
      const parts = normalizedPath?.split(/[\\/]/) || [];
      // Find the index of the 'RigDocs' folder in the path
      const rigDocsIndex = parts.findIndex((p: any) => p === 'RigDocs');
      const topFolder = rigDocsIndex >= 0 ? parts[rigDocsIndex + 1] : null;

      if (!topFolder) continue;

      // Initialize the group array for this folder if not already created
      if (!folderGroups[topFolder]) folderGroups[topFolder] = [];
      const lastModified = doc.lastModifiedAt || doc.MODIFIED_AT || doc.CREATED_AT || Date.now();
      folderGroups[topFolder].push({
        id: doc.id,
        name: doc.name,
        localPath: normalizedPath, 
        type: doc.isFolder ? 'folder' : 'file',
        lastModifiedAt: String(Math.floor(new Date(lastModified).getTime() / 1000))
      });
    }
    const customFileNames: { [folderName: string]: string } = {
      'HSE MS': 'Documents.json',
      'CTA': 'Critical Task Assessments.json',
      'Hazard Assessments': 'Hazard Assessments and Work Instructions.json',
      'Updated Rig Doc Sync UAT Folder': 'RigDocSync UAT.json',
      'Placemats': 'PD Equip. Placemats.json',
    };

    // Iterate through each grouped folder to create its JSON file
    for (const [folderName, items] of Object.entries(folderGroups)) {
      const buffer = new TextEncoder().encode(JSON.stringify(items, null, 2)).buffer;

      const fileName = customFileNames[folderName] || `${folderName}.json`;

      const firstPath = items[0]?.localPath;
      let jsonDir = '';

      if (firstPath) {
        const parts = firstPath.split(/[\\/]/);
        const rigDocsIndex = parts.findIndex((p: any) => p === 'RigDocs' || p === 'Documents');
        if (rigDocsIndex >= 0) {
          const rigDocsPath = parts.slice(0, rigDocsIndex + 1).join('/');
          jsonDir = rigDocsPath;
        }
      }
      const basePathForJson = getBasePath().replace(/\/+$/, ''); 
      let fullJsonPath = '';

      if (jsonDir && /^file:\/\//i.test(jsonDir)) {
        fullJsonPath = `${jsonDir.replace(/\/+$/, '')}/${fileName}`;
      } else if (jsonDir) {
        const cleanedJsonDir = jsonDir.replace(/^\/+|\/+$/g, '');
        fullJsonPath = `${basePathForJson}/${cleanedJsonDir}/${fileName}`;
      } else {
        fullJsonPath = `${basePathForJson}/${fileName}`;
      }

      // Preserve scheme and collapse duplicate slashes only in path portion
      fullJsonPath = this.sanitizePathPreserveFileScheme(fullJsonPath);

      if (isElectron() && /^\/[A-Za-z]:\//.test(fullJsonPath)) {
        fullJsonPath = fullJsonPath.substring(1);
      }
      try {
        console.log(`Writing JSON to: ${fullJsonPath}`);
        await this.attachmentHelper.writeExternalFile(fullJsonPath, buffer);
      } catch (err) {
        console.error(`Failed to write ${fileName}:`, err);
      }
    }
  }

}