import { Inject, Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import * as uuid from 'uuid';
import { BehaviorSubject, from, Observable } from 'rxjs';
import { File } from '@awesome-cordova-plugins/file/ngx'
import { Store } from '@ngrx/store';
import { firstValueFrom } from 'rxjs';
import { AppState } from '../store/app.state';
import { selectRigLoadedFromDb, selectRigData, selectTemplatesLoadedFromDb, selectAllTemplates } from '../store/store.selector';
import { ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AppConstants } from '../constants/appConstants';
import { SETTING_HEADER } from 'src/models/SETTING_HEADER';
import moment from 'moment-timezone';
import { TOPIC_HEADER } from 'src/models/TOPIC_HEADER';
import { CTA_HEADER } from 'src/models/CTA_HEADER';
import { HSE_STANDARD_HEADER } from 'src/models/HSE_STANDARD_HEADER';
import { TMPLT_VER } from 'src/models/TMPLT_VER';
@Injectable({
  providedIn: 'root'
})
export class UtilityService {
   
  private currentRunModeValue?: String;
  private langSubject = new BehaviorSubject<string>(localStorage.getItem('lang') || 'en');
  lang$ = this.langSubject.asObservable();

  constructor(private translate: TranslateService , private unviredSdk: UnviredCordovaSDK, private store: Store<AppState>, private file: File,) {
    this.setLanguage(this.langSubject.value); // initialize
  }

  changeLanguage(lang: string) {
    localStorage.setItem('lang', lang);
    this.setLanguage(lang);
    this.langSubject.next(lang);
  }

  private setLanguage(lang: string) {
    this.translate.use(lang);
    document.dir = lang === 'ar' ? 'rtl' : 'ltr';
  }

  getCurrentLang(): Observable<string> {
    return this.lang$;
  }

callGetMessages(): Observable<any> {
  return new Observable((observer) => {
    try {
      const response = this.unviredSdk.getMessages();
      observer.next(response);
      observer.complete();
    } catch (error) {
      observer.error(error);
    }
  });
}
public guid32() {
		let guid = uuid.v4();
		let guid32 = guid.replace(/-/g, "");
		return guid32;
	}

public returnDisplayDate(time: any, fromFormat?: string, toFormat?: string): string {
  try {
    if (!time) return '';
    toFormat = toFormat || 'MMM DD YYYY';
    if (fromFormat) {
      // Use provided format if specified
      return moment.utc(time, fromFormat).local().format(toFormat);
    } else {
      // Let moment guess ISO8601/Javascript date
      return moment.utc(time).local().format(toFormat);
    }
  } catch (e) {
    this.unviredSdk.logError("utilityService", "returnDisplayDate", "Invalid Date format");
    return '';
  }

}
getTimeZone(){
const timezone = moment.tz.guess();
return timezone
}
/**
   * Returns new STMR ID in format: 'STMRYYYYMMDDRIGHHmmss000001'
   * @param currIndex Number of current STMRs (for incrementing index)
   * @param rigNo Rig number to embed in ID
   */
  public genStmrId(currIndex: number = 0, rigNo: number | string = 0): string {
    const time = moment();

    const datePart = time.format('YYYYMMDD');
    const timePart = time.format('HHmmss');
    const paddedRigNo = this.addPositiveZeros(rigNo.toString(), 3);
    const paddedIndex = this.addPositiveZeros((currIndex + 1).toString(), 6);

    return `STMR${datePart}${paddedRigNo}${timePart}${paddedIndex}`;
  }

  /**
   * Pads a number with leading zeros to reach the desired length.
   * @param value Number or string to pad
   * @param length Desired total length after padding
   */
  public addPositiveZeros(value: string | number, length: number): string {
    return value.toString().padStart(length, '0');
  }

  private isCompanyLogoSet: boolean = AppConstants.BOOL_FALSE;

  /**
   * Returns Promise<boolean> indicating whether company logo is set.
   */
  public checkCompanyLogoStatus(): Promise<boolean> {
    return Promise.resolve(this.isCompanyLogoSet);
  }
  /**
   * Returns true if Rig data and Templates have been fully loaded from DB.
   */
  public async checkIfAllInitDataDownloaded(): Promise<boolean> {
    const rigLoaded = !!(await firstValueFrom(this.store.select(selectRigLoadedFromDb)));
    const rigData = await firstValueFrom(this.store.select(selectRigData));
    const templatesLoaded = !!(await firstValueFrom(this.store.select(selectTemplatesLoadedFromDb)));
    const templates = await firstValueFrom(this.store.select(selectAllTemplates));

    const isValidRig = rigLoaded && !!rigData?.RIG_NO;
    const isValidTemplates = templatesLoaded && Array.isArray(templates) && templates.length > 0;

    return isValidRig && isValidTemplates;
  }

/**
 * Returns Status description object based on sync status
 * @param syncStatus sync status code (0: NONE, 1: QUEUED, 2: SENT, 3: ERROR)
 */
getSyncStatusObj(syncStatus?: number): { descr?: string; color?: string } {
  let statusDescObj: { descr?: string; color?: string } = {};
  // SYNC_STATUS can be NONE: 0, QUEUED: 1, SENT: 2, ERROR: 3
  // If NONE: 0 do not display send {}
  switch (syncStatus) {
    case 1:
      statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.QUEUED;
      break;
    case 2:
      statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.SENT;
      break;
    case 3:
      statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.ERROR;
      break;
    default:
      break;
  }
  return statusDescObj;
}

  // Helpers used by TopicsPage
  public sortArray(array: any, sortKey: string) {
		if (array && array.length && sortKey) {
			array = array.sort((oldVal: any, newVal: any): number => {
				if (!oldVal[sortKey] || !newVal[sortKey]) {
					return 0
				}
				if (oldVal[sortKey].toLowerCase() > newVal[sortKey].toLowerCase()) return 1;
				if (oldVal[sortKey].toLowerCase() < newVal[sortKey].toLowerCase()) return -1;
				return 0;
			})
			return array;
		} else return [];
	}

  public getTopicName(topics: TOPIC_HEADER[], topicId: string) {
		for (let i = 0, iLen = topics.length; i < iLen; i++) {
			if (topics[i].TOPIC_ID == topicId)
				return topics[i].TOPIC_NAME;
		}
		return "";
	}

  public getCTADesc(ctas: CTA_HEADER[], ctaId: string) {
		for (let i = 0, iLen = ctas.length; i < iLen; i++) {
			if (ctas[i].CTA_ID == ctaId)
				return ctas[i].DESCR;
		}
		return "";
	}

  public getHSEDesc(hses: HSE_STANDARD_HEADER[], ctaId: string) {
		for (let i = 0, iLen = hses.length; i < iLen; i++) {
			if (hses[i].STD_ID == ctaId)
				return hses[i].DESCR;
		}
		return "";
	}

  public getReleaseVersionDet(
  tmpltVer: any[],
  keys: string[]
): { [key: string]: any } {
  const result: { [key: string]: any } = {};

  if (keys.length > 0 && tmpltVer?.length) {
    // Find the first template with REL status
    const releasedVer = tmpltVer.find(
      ver => ver?.STATUS === AppConstants.VAL_TMPLT_STATUS.REL
    );

    if (releasedVer) {
      for (const key of keys) {
        result[key] = releasedVer[key];
      }
    }
  }

  return result;
}




  /**
   * Get local timezone identifier
   */
  public getTimezone(): string {
		let timezone = moment.tz.guess()
		return timezone
	}

 public async getCTALocation(): Promise<string> {
  try {
    const result = await this.unviredSdk.dbSelect(
      AppConstants.TABLE_SETTING_HEADER,
      { NAME: AppConstants.SETTING_CTA_LOCAL_PATH }
    );

    if (result.data && result.data.length > 0) {
      let settingsHeader: SETTING_HEADER = result.data[0];
      settingsHeader.VALUE = settingsHeader.VALUE.replace(/\\\\/g, '\\');
      return settingsHeader.VALUE;
    } else {
      this.unviredSdk.logError(
        "Utility",
        "getCTALocation",
        `Result Set is empty for Setting: ${AppConstants.SETTING_CTA_LOCAL_PATH}`
      );
      throw new Error(
        `Result Set is empty for Setting: ${AppConstants.SETTING_CTA_LOCAL_PATH}`
      );
    }
  } catch (error: any) {
    this.unviredSdk.logError(
      "Utility",
      "getCTALocation",
      `Error While retrieving CTA Local Path: ${error}`
    );
    throw new Error(`Error While retrieving CTA Local Path: ${error}`);
  }
}


  public async getHSELocation(): Promise<string> {
  try {
    const result = await this.unviredSdk.dbSelect(
      AppConstants.TABLE_SETTING_HEADER,
      { NAME: AppConstants.SETTING_HSE_LOCAL_PATH }
    );

    if (result.data && result.data.length > 0) {
      let settingsHeader: SETTING_HEADER = result.data[0];
      settingsHeader.VALUE = settingsHeader.VALUE.replace(/\\\\/g, '\\');
      return settingsHeader.VALUE;
    } else {
      this.unviredSdk.logError(
        "Utility",
        "getHSELocation",
        `Result Set is empty for Setting: ${AppConstants.SETTING_HSE_LOCAL_PATH}`
      );
      throw new Error(
        `Result Set is empty for Setting: ${AppConstants.SETTING_HSE_LOCAL_PATH}`
      );
    }
  } catch (error: any) {
    this.unviredSdk.logError(
      "Utility",
      "getHSELocation",
      `Error While retrieving HSE Local Path: ${error}`
    );
    throw new Error(`Error While retrieving HSE Local Path: ${error}`);
  }
}


  /**
   * Returns Status description object based on form status
   */
  public getFormStatusObj(status: any) {
    let statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.OPEN;
    switch (status) {
      case AppConstants.VAL_FORM_STATUS.OPEN:
      default:
        statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.OPEN;
        break;
      case AppConstants.VAL_FORM_STATUS.INPR:
        statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.INPR;
        break;
      case AppConstants.VAL_FORM_STATUS.SUBM:
        statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.SUBM;
        break;
      case AppConstants.VAL_FORM_STATUS.CAN_BE_SUBM:
        statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.CAN_BE_SUBM;
        break;
      case AppConstants.VAL_FORM_STATUS.SKIP:
        statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.SKIP;
        break;
    }
    return statusDescObj;
  }

  /**
   * Check if app is running in test automation mode (cached)
   */
  public async isAppRunningInTestAutomationMode(): Promise<boolean> {
  try {
    // Return immediately if we already know the value
    if (this.currentRunModeValue === AppConstants.SETTING_MODE_TEST_AUTOMATION) {
      return true;
    } else if (this.currentRunModeValue === AppConstants.SETTING_MODE_PRODUCTION_RUN) {
      return false;
    }

    // If undefined, fetch from DB
    const result = await this.unviredSdk.dbSelect(
      AppConstants.TABLE_SETTING_HEADER,
      { NAME: AppConstants.SETTING_RUN_MODE }
    );

    if (result.data && result.data.length > 0) {
      const settingsHeader: SETTING_HEADER = result.data[0];
      this.currentRunModeValue = settingsHeader.VALUE;
    } else {
      this.unviredSdk.logInfo(
        "Utility",
        "getRunMode",
        `Result Set is empty for Setting: ${AppConstants.SETTING_RUN_MODE}`
      );
      // Default to production run if not set
      this.currentRunModeValue = AppConstants.SETTING_MODE_PRODUCTION_RUN;
    }

    return this.currentRunModeValue === AppConstants.SETTING_MODE_TEST_AUTOMATION;
  } catch (error: any) {
    this.unviredSdk.logError(
      "Utility",
      "getRunMode",
      `Error While retrieving Run Mode: ${error}`
    );
    throw new Error(`Error While retrieving Run Mode: ${error}`);
  }
}


  /**
   * Delete persisted app state file, if present
   */
  public async deleteAppState(): Promise<void> {
    try {
      const basePath: string = this.file.dataDirectory;
      await this.file.removeFile(basePath, 'lastSavedForm.json');
    } catch {
      // ignore
    }
  }

  /**
   * Insert or update crew members list in database
   */
  public async insertOrUpdateCrewList(crewList: any[]): Promise<void> {
    try {
      console.log('[UtilityService] insertOrUpdateCrewList called with:', crewList.length, 'crew members');

      for (let i = 0; i < crewList.length; i++) {
        const crew = crewList[i];
        console.log(`[UtilityService] Saving crew member ${i + 1}:`, crew);

        // Create a clean object with only database fields
        const cleanCrew = {
          PERSON_NO: crew.PERSON_NO,
          USER_ID: crew.USER_ID || '',
          USER_NAME: crew.USER_NAME,
          DESIGNATION: crew.DESIGNATION,
          COMP_CODE: crew.COMP_CODE || '',
          RIG_NO: crew.RIG_NO,
          EMAIL: crew.EMAIL || '',
          MAINPH: crew.MAINPH || '',
          CELPH: crew.CELPH || '',
          GLOBALID: crew.GLOBALID || '',
          IS_MANAGER: crew.IS_MANAGER || '',
          SOURCE: crew.SOURCE
        };

        console.log(`[UtilityService] Clean crew object for ${crew.USER_NAME}:`, cleanCrew);

        const result = await this.unviredSdk.dbInsertOrUpdate(
          AppConstants.TABLE_CREW_HEADER,
          cleanCrew,
          false
        );

        console.log(`[UtilityService] Save result for ${crew.USER_NAME}:`, result);

        if (result.type !== ResultType.success) {
          throw new Error(`Failed to save crew member: ${crew.USER_NAME} - ${JSON.stringify(result.error)}`);
        }
      }
      console.log('[UtilityService] Successfully saved all crew members to database');
    } catch (error) {
      console.error('[UtilityService] Error saving crew members:', error);
      throw error;
    }
  }
}



