import { createAction, props } from "@ngrx/store";
import { RIG_HEADER } from 'src/models/RIG_HEADER';
import { NotifResult } from "@awesome-cordova-plugins/unvired-cordova-sdk/ngx";
import { COMPANY_HEADER } from "src/models/COMPANY_HEADER";
import { CREW_HEADER } from "src/models/CREW_HEADER";
import { OPERATOR_HEADER } from "src/models/OPERATOR_HEADER";
import { C_NOTIF_TYPE_HEADER } from "src/models/C_NOTIF_TYPE_HEADER";
import { C_PRIORITY_HEADER } from "src/models/C_PRIORITY_HEADER";
import { C_CODE_GROUP_HEADER } from "src/models/C_CODE_GROUP_HEADER";
import { C_CODE_HEADER } from "src/models/C_CODE_HEADER";


export const increment = createAction('[Counter Increment ]')

export const decrement = createAction('[Counter decrement ]')

export const reset = createAction('[Counter reset ]')

export const incrementAsync = createAction('[Counter] Increment Async');

export const switchRig = createAction(
  '[Rig] Switch Rig',
  props<{ rigId: string, deviceId: string }>()
);

export const switchRigSuccess = createAction(
  '[Rig] Switch Rig Success',
  props<{ rigData: RIG_HEADER }>()
);

export const switchRigFailure = createAction(
  '[Rig] Switch Rig Failure',
  props<{ error: any }>()
);

export const clearRigState = createAction('[Rig] Clear Rig State');


export const loadRigFromDb = createAction(
  '[Rig] Load Rig From DB'
);

export const loadRigFromDbSuccess = createAction(
  '[Rig] Load Rig From DB Success',
  props<{ rigData: any }>()
);

export const loadRigFromDbFailure = createAction(
  '[Rig] Load Rig From DB Failure',
  props<{ error: any }>()
);

export const addNotification = createAction(
  '[Notification] Add Notification',
  props<{ notification: NotifResult }>()
);


export const loadAllTemplatesFromDb = createAction('[Rig] Load All Templates From DB');

export const loadAllTemplatesDbSuccess = createAction(
  '[Rig] Load All Templates DB Success',
  props<{ templates: any[] }>()
);

export const loadAllTemplatesDbFailure = createAction(
  '[Rig] Load All Templates DB Failure',
  props<{ error: any }>()
);

export const clearTemplateState = createAction('[Rig] Clear Template State');

export const loadProgressBar = createAction('[Progress] Load Progress Bar');

export const loadProgressBarSuccess = createAction(
  '[Progress] Load Progress Bar Success',
  props<{ percentage: number }>()
);

export const loadProgressBarFailure = createAction(
  '[Progress] Load Progress Bar Failure',
  props<{ error: any }>()
);


export const triggerPrefillRefresh = createAction(
  '[Crew] Trigger Prefill Refresh'
);

export const prefillRefreshed = createAction(
  '[Crew] Prefill Refreshed'
);

// COMPANY
export const loadCompanyHeader = createAction('[Data] Load Company Header');
export const loadCompanyHeaderSuccess = createAction('[Data] Load Company Header Success', props<{ data: COMPANY_HEADER }>());
export const loadCompanyHeaderFailure = createAction('[Data] Load Company Header Failure', props<{ error: any }>());

// CREW
export const loadCrewHeader = createAction('[Data] Load Crew Header');
export const loadCrewHeaderSuccess = createAction('[Data] Load Crew Header Success', props<{ data: CREW_HEADER }>());
export const loadCrewHeaderFailure = createAction('[Data] Load Crew Header Failure', props<{ error: any }>());

// OPERATOR
export const loadOperatorHeader = createAction('[Data] Load Operator Header');
export const loadOperatorHeaderSuccess = createAction('[Data] Load Operator Header Success', props<{ data: OPERATOR_HEADER }>());
export const loadOperatorHeaderFailure = createAction('[Data] Load Operator Header Failure', props<{ error: any }>());


export const loadNotifTypeHeader = createAction( '[Rig] Load Notif Type Header');
export const loadNotifTypeHeaderSuccess = createAction('[Rig] Load Notif Type Header Success', props<{ data: C_NOTIF_TYPE_HEADER }>());
export const loadNotifTypeHeaderFailure = createAction('[Rig] Load Notif Type Header Failure', props<{ error: any }>());


export const loadPriorityHeader = createAction('[Rig] Load Priority Header');
export const loadPriorityHeaderSuccess = createAction('[Rig] Load Priority Header Success', props<{ data: C_PRIORITY_HEADER }>());
export const loadPriorityHeaderFailure = createAction('[Rig] Load Priority Header Failure', props<{ error: any }>());


export const loadCodeGroupHeader = createAction('[Rig] Load Code Group Header');
export const loadCodeGroupHeaderSuccess = createAction('[Rig] Load Code Group Header Success', props<{ data: C_CODE_GROUP_HEADER }>());
export const loadCodeGroupHeaderFailure = createAction( '[Rig] Load Code Group Header Failure', props<{ error: any }>());


export const loadCodeHeader = createAction( '[Rig] Load Code Header');
export const loadCodeHeaderSuccess = createAction('[Rig] Load Code Header Success', props<{ data: C_CODE_HEADER }>());
export const loadCodeHeaderFailure = createAction('[Rig] Load Code Header Failure', props<{ error: any }>());


export const loadPrefilledData = createAction('[Rig] Load Prefilled Data');
export const loadPrefilledDataSuccess = createAction('[Rig] Load Prefilled Data Success', props<{ prefilledData: any }>());
export const loadPrefilledDataFailure = createAction('[Rig] Load Prefilled Data Failure', props<{ error: any }>());

// SYNC (Rig Docs)
export const rigDocSyncStart = createAction('[Sync] Rig Doc Sync Start', props<{ triggeredByUser: boolean }>());
export const rigDocSyncEnd = createAction('[Sync] Rig Doc Sync End');
export const rigDocSyncError = createAction('[Sync] Rig Doc Sync Error', props<{ error: any }>());

export const syncSetIsSyncing = createAction('[Sync] Set IsSyncing', props<{ isSyncing: boolean }>());
export const syncSetProgress = createAction('[Sync] Set Progress', props<{ progress: number }>());
export const syncSetCurrentFile = createAction('[Sync] Set Current File', props<{ currentSyncFile: string }>());

export const loadGraphToken = createAction('[Sync] Load Graph Token');
export const loadGraphTokenSuccess = createAction('[Sync] Load Graph Token Success', props<{ token: string }>());
export const loadGraphTokenFailure = createAction('[Sync] Load Graph Token Failure', props<{ error: any }>());

export const loadSharepointSites = createAction('[Sync] Load SharePoint Sites');
export const loadSharepointSitesSuccess = createAction('[Sync] Load SharePoint Sites Success', props<{ sites: any[] }>());
export const loadSharepointSitesFailure = createAction('[Sync] Load SharePoint Sites Failure', props<{ error: any }>());

export const loadSiteMeta = createAction('[Sync] Load Site Meta');
export const loadSiteMetaSuccess = createAction('[Sync] Load Site Meta Success', props<{ siteMeta: any[] }>());
export const loadSiteMetaFailure = createAction('[Sync] Load Site Meta Failure', props<{ error: any }>());

export const updateDeviceSiteMeta = createAction('[Sync] Update Device Site Meta', props<{ documents: any[] }>());
export const updateDeviceSiteMetaSuccess = createAction('[Sync] Update Device Site Meta Success');
export const updateDeviceSiteMetaFailure = createAction('[Sync] Update Device Site Meta Failure', props<{ error: any }>());

export const checkServerSiteMeta = createAction('[Sync] Check Server Site Meta');
export const checkServerSiteMetaSuccess = createAction('[Sync] Check Server Site Meta Success', props<{ raw: any }>());
export const checkServerSiteMetaFailure = createAction('[Sync] Check Server Site Meta Failure', props<{ error: any }>());

// Ensure Site Headers polling
export const ensureSiteHeadersStart = createAction('[Sync] Ensure Site Headers Start');
export const ensureSiteHeadersStop = createAction('[Sync] Ensure Site Headers Stop');