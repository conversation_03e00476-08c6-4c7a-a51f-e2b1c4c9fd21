import { createReducer , on } from "@ngrx/store";
import { increment, decrement, reset, addNotification } from './store.actions';
import * as RigActions from './store.actions';
import { SyncState } from './app.state';
import { NotificationState, PrefilledState, ProgressState, RigState, TemplateState } from "./app.state";
export interface CounterState{
    counter : number
}


export const initialCountState: CounterState = {
    counter: 0
}

export const counterReducer = createReducer( 
    initialCountState,
    on(increment , state => ({...state, counter: state.counter + 1})),
    on(decrement , state => ({...state, counter: state.counter - 1})),
    on(reset, state => ({...state, counter: 0}))
)


export const initialRigState: RigState = {
  rigData: null,
  error: null,
  loadedFromDb: false,
  loadedFromServer: false,
  prefillData: null,
  prefillNeedsRefresh: false 
};

export const rigReducer = createReducer(
  initialRigState,
on(RigActions.switchRigSuccess, (state, { rigData }) => ({
  ...state,
  rigData,
  loadedFromServer: true,
  error: null

})),
  on(RigActions.switchRigFailure, (state, { error }) => ({
    ...state,
    error
  })),
  on(RigActions.clearRigState, (state) => ({
    ...state,
    rigData: null,
    error: null
  })),


  on(RigActions.loadRigFromDbSuccess, (state, { rigData }) => ({
    ...state,
    rigData,
    loadedFromDb: true, // ✅ MUST SET THIS
    error: null
  })), 
  on(RigActions.loadRigFromDbFailure, (state, { error }) => ({
    ...state,
    rigData: null,
    loadedFromDb: true, // ✅ still set this to true so the selector emits
    error
  })),


  on(RigActions.loadPrefilledDataSuccess, (state, { prefilledData }) => ({
    ...state,
    prefillData: prefilledData,
    prefillNeedsRefresh: false
  })),

 on(RigActions.triggerPrefillRefresh, state => ({
   ...state,
   prefillNeedsRefresh: true
 })),
 on(RigActions.prefillRefreshed, state => ({
   ...state,
   prefillNeedsRefresh: false
 }))
);


const initialNotifState: NotificationState = {
  events: [],
};

export const notificationReducer = createReducer(
  initialNotifState,
  on(addNotification, (state, { notification }) => ({
    ...state,
    events: [...state.events, notification], // ✅ appends new notification
  }))
);

export const initialTemplateState: TemplateState = {
  templates: [],
  error: null,
  loadedFromDb: false,
  loadedFromServer: false,
};


export const templateReducer = createReducer(
  initialTemplateState,

  on(RigActions.loadAllTemplatesDbSuccess, (state, { templates }) => {
    console.log('Reducer updating templates:', templates); // ✅ Add this for debugging
    return {
      ...state,
      templates,             // Add the loaded templates to the state
      loadedFromDb: true,    // Mark as loaded
      error: null,           // Clear any previous errors
    };
  }),
  
  on(RigActions.loadAllTemplatesDbFailure, (state, { error }) => ({
    ...state,
    error,                // Store the error
    loadedFromDb: false,  // Mark as not loaded due to failure
  })),
  

  on(RigActions.loadAllTemplatesFromDb, (state) => ({
    ...state,
    loadedFromDb: false,  // Mark as not loaded when the load action is dispatched
  })),

  on(RigActions.clearTemplateState, (state) => ({
    ...state,
    templates: [],
    error: null,
    loadedFromDb: false,
    loadedFromServer: false,
  })),



);

export const initialState: ProgressState = {
  percentage: 0,
  error: null,
};

export const progressReducer = createReducer(
  initialState,
  on(RigActions.loadProgressBarSuccess, (state, { percentage }) => ({
    ...state,
    percentage,
    error: null
  })),
  on(RigActions.loadProgressBarFailure, (state, { error }) => ({
    ...state,
    error
  }))
);


export const initialPrefilledState: PrefilledState = {
  prefilledData: null,
};

export const prefilledReducer = createReducer(
  initialPrefilledState,


  on(RigActions.loadPrefilledDataSuccess, (state, { prefilledData }) => {
  console.log('[Reducer] Loaded prefilled data:', prefilledData);
  return {
    ...state,
    prefilledData,
  };
})
);

// SYNC slice
export const initialSyncState: SyncState = {
  isSyncing: false,
  progress: 0,
  currentSyncFile: '',
  graphToken: null,
  siteHeaders: [],
  siteMeta: [],
  baseLocalPath: null,
  error: null,
};

export const syncReducer = createReducer(
  initialSyncState,
  on(RigActions.syncSetIsSyncing, (state, { isSyncing }) => ({ ...state, isSyncing })),
  on(RigActions.syncSetProgress, (state, { progress }) => ({ ...state, progress })),
  on(RigActions.syncSetCurrentFile, (state, { currentSyncFile }) => ({ ...state, currentSyncFile })),

  on(RigActions.loadGraphTokenSuccess, (state, { token }) => ({ ...state, graphToken: token, error: null })),
  on(RigActions.loadGraphTokenFailure, (state, { error }) => ({ ...state, error })),

  on(RigActions.loadSharepointSitesSuccess, (state, { sites }) => ({ ...state, siteHeaders: sites as any, error: null })),
  on(RigActions.loadSharepointSitesFailure, (state, { error }) => ({ ...state, error })),

  on(RigActions.loadSiteMetaSuccess, (state, { siteMeta }) => ({ ...state, siteMeta, error: null })),
  on(RigActions.loadSiteMetaFailure, (state, { error }) => ({ ...state, error })),

  on(RigActions.updateDeviceSiteMetaFailure, (state, { error }) => ({ ...state, error })),
  on(RigActions.checkServerSiteMetaFailure, (state, { error }) => ({ ...state, error })),

  on(RigActions.rigDocSyncStart, (state) => ({ ...state, isSyncing: true, progress: 0, currentSyncFile: '', error: null })),
  on(RigActions.rigDocSyncEnd, (state) => ({ ...state, isSyncing: false, progress: 0, currentSyncFile: '' })),
  on(RigActions.rigDocSyncError, (state, { error }) => ({ ...state, isSyncing: false, error }))
  ,
  on(RigActions.ensureSiteHeadersStart, (state) => state)
  ,
  on(RigActions.ensureSiteHeadersStop, (state) => state)
);




