.input-group {
    position: relative;
    display: table;
    border-collapse: separate;
    .form-control {
        position: relative;
        z-index: 2;
        float: left;
        width: 100%;
        margin-bottom: 0;
        &:focus {
            z-index: 3;
        }
        display: table-cell;
    }
}

.input-group-addon {
    display: table-cell;
}

.input-group .form-control:not(:first-child):not(:last-child) {
    border-radius: 0;
}

.input-group-addon {
    &:not(:first-child):not(:last-child) {
        border-radius: 0;
    }
    width: 1%;
    white-space: nowrap;
    vertical-align: middle;
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1;
    color: #555;
    text-align: center;
    background-color: #eee;
    border: 1px solid #ccc;
    border-radius: 4px;
    &.input-sm {
        padding: 5px 10px;
        font-size: 12px;
        border-radius: 3px;
    }
    &.input-lg {
        padding: 10px 16px;
        font-size: 18px;
        border-radius: 6px;
    }
    input {
        &[type=checkbox],
        &[type=radio] {
            margin-top: 0;
        }
    }
}

.input-group .form-control:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group-addon:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: 0;
}

.input-group .form-control:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.input-group-addon:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
}