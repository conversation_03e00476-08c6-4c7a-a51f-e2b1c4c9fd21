.contrast {
    background: #000;
    .header-b {
        background-color: #FFF!important;
        color: #000!important;
    }
    .item-block {
        background-color: #000!important;
        color: #FFF!important;
    }
    .span,
    .color-grey,
    .grey,
    .black {
        color: #FFF;
    }
    .item-input .label-md,
    .item-select .label-md,
    .item-datetime .label-md {
        color: #FFF;
    }
    .item-md p,
    .fade {
        color: rgb(201, 201, 201);
    }
    .fade:after {
        background: -moz-linear-gradient(left, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.78) 58%, rgba(0, 0, 0, 1) 74%, rgba(0, 0, 0, 1) 100%);
        /* FF3.6-15 */
        background: -webkit-linear-gradient(left, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.78) 58%, rgba(0, 0, 0, 1) 74%, rgba(0, 0, 0, 1) 100%);
        /* Chrome10-25,Safari5.1-6 */
        background: linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.78) 58%, rgba(0, 0, 0, 1) 74%, rgba(0, 0, 0, 1) 100%);
        /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    }
    .input-underline {
        border-bottom-color: #FFF;
    }
    .input-group-addon,
    .crew-addon {
        border-color: rgb(239, 239, 254) !important;
        color: white;
    }
    textarea {
        background-color: #000;
        color: #FFF;
    }
    .disabled {
        opacity: 0.7;
    }
    .has-error input {
        border-bottom-color: rgb(252, 252, 9)!important;
    }
    .has-error,
    .has-error h2,
    .has-error ion-label,
    .has-error ion-select .select-placeholder,
    .has-error .input-group .crew-addon {
        color: rgb(252, 252, 9)!important;
    }
    .has-error .checkbox-md .checkbox-icon,
    .has-error .input-group .crew-addon {
        border-color: rgb(252, 252, 9)!important;
    }
    .has-error input::placeholder,
    .has-error textarea::placeholder {
        /* Chrome, Firefox, Opera, Safari 10.1+ */
        color: rgb(252, 252, 9);
        opacity: 1;
        /* Firefox */
    }
    .has-error input:-ms-input-placeholder,
    .has-error textarea:-ms-input-placeholder {
        /* Internet Explorer 10-11 */
        color: rgb(252, 252, 9);
    }
    .has-error input::-ms-input-placeholder,
    .has-error textarea::-ms-input-placeholder {
        /* Microsoft Edge */
        color: rgb(252, 252, 9);
    }
}