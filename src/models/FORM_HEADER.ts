import { DATA_STRUCTURE } from "./DATA_STRUCTURE";
import { UtilityService } from "src/app/services/utility.service";

export class FORM_HEADER extends DATA_STRUCTURE {
    FORM_ID: string = UtilityService.prototype.guid32();
    VER_ID: string = '';
    CRTD_BY: string = '';
    CRTD_ON: number = 0;
    SUBM_BY: string = '';
    DATE_COMP: number = 0;
    COMPANY: string = '';
    RIG_NO: string = '';
    COMMENTS: string = '';
    FORM_STATUS: string = '';
    LAST_SYNC_USER: string = '';
    LAST_SYNC_TIME: number = 0;
    P_MODE: string = '';
    TIME_ZONE: string = '';
}