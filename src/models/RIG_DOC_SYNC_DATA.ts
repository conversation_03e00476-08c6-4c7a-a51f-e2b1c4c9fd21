import { SP_CONFIG_HEADER } from './SP_CONFIG_HEADER';
import { SP_SITE_DOC } from './SP_SITE_DOC';
import { SP_SITE_META_HEADER } from './SP_SITE_META_HEADER';
import { SP_SITE_HEADER } from './SP_SITE_HEADER';
import { SP_SITE_LIB } from './SP_SITE_LIB';

export class RIG_DOC_SYNC_DATA {
    private config: SP_CONFIG_HEADER[];
    private spSiteHeader: SP_SITE_HEADER;
    private spSiteLibrary: SP_SITE_LIB;
    private spSiteDoc: SP_SITE_DOC[];
    private spSiteMeta: SP_SITE_META_HEADER;

    constructor(spSiteHeader: SP_SITE_HEADER, spSiteLibrary: SP_SITE_LIB, spSiteDoc: SP_SITE_DOC[],  spSiteMeta: SP_SITE_META_HEADER, config: SP_CONFIG_HEADER[]) {    
        this.config = config;
        this.spSiteDoc = spSiteDoc;
        this.spSiteMeta = spSiteMeta;
        this.spSiteHeader = spSiteHeader;
        this.spSiteLibrary = spSiteLibrary;
    }

    getSiteMetaHeader(): SP_SITE_META_HEADER {
        return this.spSiteMeta;
    }
    
    async getCredentialsJSON(): Promise<any> {

        return new Promise(async (resolve, reject) => {

            let clientSecret  = this.config.filter(c => c.NAME == "SP_CLIENT_SECRET")[0].VALUE
            let decryptedClientSecret = atob(clientSecret);

            resolve(JSON.stringify({
                "tenant-id": this.config.filter(c => c.NAME == "SP_TENANT_ID")[0].VALUE,
                "client-id": this.config.filter(c => c.NAME == "SP_CLIENT_ID")[0].VALUE,
                "client-secret": decryptedClientSecret,
                "sitepath": this.spSiteHeader.SP_SITE_NAME,
                "library": this.spSiteLibrary.DOC_LIBRARY,
                "folder": this.spSiteLibrary.FOLDER_NAME,
                "root": this.config.filter(c => c.NAME == "SP_LOCAL_ROOT")[0].VALUE
            }));
        });
    }

    getServerMetaJSON(): any {

        let serverMetaJSON: any[] = [];

        this.spSiteDoc.forEach(doc => {
            serverMetaJSON.push({
                "id": doc.DOC_ID,
                "type": doc.IS_FOLDER == "true" ? "folder" : "file",
                "name": doc.DOC_NAME,
                "lastModifiedAt": doc.MODIFED_AT,
                "localpath": doc.LOCAL_PATH,
                "modifiedBy": doc.MODIFIED_BY,
                "version": doc.VERSION
            });
        });
        return JSON.stringify(serverMetaJSON);
    }

    getServerMetaDocuments(): SP_SITE_DOC[] {
        return this.spSiteDoc;
    }

    getDocLibraryName(): string {
        return this.spSiteLibrary.DOC_LIBRARY;
    }

    static getSiteMetaDocumentsFromJSON(json: string, spSiteHeader: SP_SITE_META_HEADER): SP_SITE_DOC[] {

        let siteMetaJSON = JSON.parse(json);
        let siteMetaDocuments: SP_SITE_DOC[] = [];

        siteMetaJSON.forEach((doc: any)  => {
            let newSpSiteDoc = new SP_SITE_DOC()
            newSpSiteDoc.SP_SITE_ID = spSiteHeader.SP_SITE_ID;
            newSpSiteDoc.DOC_LIBRARY = spSiteHeader.DOC_LIBRARY;
            newSpSiteDoc.DOC_ID = doc["id"];
            newSpSiteDoc.VERSION = doc["version"];
            newSpSiteDoc.DOC_NAME = doc["name"];
            newSpSiteDoc.IS_FOLDER = doc["type"] == "folder" ? "true" : "false";
            newSpSiteDoc.MODIFIED_BY = doc["modifiedBy"];
            newSpSiteDoc.MODIFED_AT = doc["lastModifiedAt"];
            newSpSiteDoc.LOCAL_PATH = doc["localpath"];
            siteMetaDocuments.push(newSpSiteDoc);
        });

        return siteMetaDocuments;
    }
}