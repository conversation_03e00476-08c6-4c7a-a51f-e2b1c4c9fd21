import { STMR_TOPIC } from "./STMR_TOPIC";

export class STMR_CTA_TOPIC extends STMR_TOPIC {
    CTA_TYPE: string = ''
    FORMS: string[] = []
    constructor(stmrTopic: STMR_TOPIC) {
        super()
        this.STMR_ID = stmrTopic.STMR_ID;
        this.TOPIC_NO = stmrTopic.TOPIC_NO;
        this.TOPIC_ID = stmrTopic.TOPIC_ID;
        this.TOPIC_NAME = stmrTopic.TOPIC_NAME;
        this.TOPIC_NOTE =  stmrTopic.TOPIC_NOTE;
        this.TOPIC_START = stmrTopic.TOPIC_START;
        this.CTA_ID = stmrTopic.CTA_ID;
        this.P_MODE = stmrTopic.P_MODE;
    }
}