import { DATA_STRUCTURE } from "./DATA_STRUCTURE";

export class STMR_HEADER extends DATA_STRUCTURE {
    STMR_ID: string;
    RIG_NO: string;
    RIG_TYPE1: string;
    RIG_SUB_TYPE: string;
    RIG_MGR_EMAIL: string;
    COMPANY: string;
    SUBM_BY: string;
    DATE_COMP: number;
    WELL_LOC: string;
    OPERATOR: string;
    PROVINCE: string;
    SHIFT: string;
    SHIFT_TIME: number;
    CHAIRED_BY: string;
    ONSITE_MGR: string;
    ONSITE_MGR_SIGN: string;
    ONSITE_SUP: string;
    ONSITE_SUP_SIGN: string;
    STMR_STATUS: string;
    LAST_SYNC_USER: string;
    LAST_SYNC_TIME: number;
    TIME_ZONE: string;
    P_MODE: string;
    CRTD_BY: string;
    CRTD_ON: number;

    constructor(){
        super();
        this.STMR_ID = '';  
        this.RIG_NO = '';
        this.RIG_TYPE1 = '';
        this.RIG_SUB_TYPE = '';
        this.RIG_MGR_EMAIL = '';
        this.COMPANY = '';
        this.SUBM_BY = '';
        this.DATE_COMP = 0;
        this.WELL_LOC = '';
        this.OPERATOR = '';
        this.PROVINCE = '';
        this.SHIFT = '';
        this.SHIFT_TIME = 0;
        this.CHAIRED_BY = '';
        this.ONSITE_MGR = '';
        this.ONSITE_MGR_SIGN = '';
        this.ONSITE_SUP = '';
        this.ONSITE_SUP_SIGN = '';
        this.STMR_STATUS = '';
        this.LAST_SYNC_USER = '';
        this.LAST_SYNC_TIME = 0;
        this.TIME_ZONE = '';
        this.P_MODE = '';
        this.CRTD_BY = '';
        this.CRTD_ON = 0;
    }
}

export class StmrDetails {
    stmr:STMR_HEADER = new STMR_HEADER();
    details:Details[] = [];
    isSelected:Boolean;

    constructor(){
        this.stmr = new STMR_HEADER();
        this.isSelected = false;
    }
   
}

export class Details {
    topicName: string = '';
    forms: string[] = [];
    ctaType: string = '';
    stdType: string = '';
    topicPMode: string = '';
}