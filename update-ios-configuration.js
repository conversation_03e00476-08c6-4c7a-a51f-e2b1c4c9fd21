const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Step 1: Create emulator directory
const buildDir = path.join(__dirname, 'platforms', 'ios', 'build');
const emulatorDir = path.join(buildDir, 'emulator');
const deviceDir = path.join(buildDir, 'device');
const debugSimulatorDir = path.join(buildDir, 'debug-iphonesimulator');
const debugDeviceDir = path.join(buildDir, 'Debug-iphoneos');

// Create directories only if needed based on debug folder existence




// Step 2: Read app name from config.xml
const configPath = path.join(__dirname, 'config.xml');
let configContent;

try {
  configContent = fs.readFileSync(configPath, 'utf-8');
} catch (err) {
  console.error('❌ Cannot read config.xml:', err.message);
  process.exit(1);
}

// Extract app name using RegExp
const nameMatch = configContent.match(/<name>([^<]+)<\/name>/i);

if (!nameMatch) {
  console.error('❌ <name> tag not found in config.xml');
  process.exit(1);
}

const appName = nameMatch[1].trim();
console.log('✅ App name from config.xml:', appName);

// Step 3: Create symbolic links based on which debug folder exists
if (fs.existsSync(debugSimulatorDir)) {
  const targetApp = path.join('..', 'debug-iphonesimulator', `${appName}.app`);
  const linkPath = path.join(emulatorDir, `${appName}.app`);
  if (fs.existsSync(debugSimulatorDir)) {
    if (!fs.existsSync(emulatorDir)) {
      fs.mkdirSync(emulatorDir, { recursive: true });
      console.log('✅ Created emulator directory:', emulatorDir);

      try {
        if (!fs.existsSync(linkPath)) {
          execSync(`ln -s "${targetApp}" "${linkPath}"`);
          console.log(`✅ Simulator symlink created: ${linkPath} → ${targetApp}`);
        } else {
          console.log('ℹ️ Simulator symlink already exists.');
        }
      } catch (err) {
        console.error('❌ Failed to create simulator symlink:', err.message);
      }
    } else {
      console.log('ℹ️ Emulator directory already exists.');
    }
  }

}

if (fs.existsSync(debugDeviceDir)) {
  const targetDeviceApp = path.join('..', 'Debug-iphoneos', `${appName}.ipa`);
  const linkDevicePath = path.join(deviceDir, `${appName}.ipa`);

  if (fs.existsSync(debugDeviceDir)) {
    if (!fs.existsSync(deviceDir)) {
      fs.mkdirSync(deviceDir, { recursive: true });
      console.log('✅ Created device directory:', deviceDir);
      try {
        if (!fs.existsSync(linkDevicePath)) {
          execSync(`ln -s "${targetDeviceApp}" "${linkDevicePath}"`);
          console.log(`✅ Device symlink created: ${linkDevicePath} → ${targetDeviceApp}`);
        } else {
          console.log('ℹ️ Device symlink already exists.');
        }
      } catch (err) {
        console.error('❌ Failed to create device symlink:', err.message);
      }
    } else {
      console.log('ℹ️ Device directory already exists.');
    }
  }

}